# **Tài liệu Hướng dẫn và Đặc tả Quy trình Xử lý Order Plan Quảng cáo**

## **I. Giới thiệu**

Tài liệu này mô tả chi tiết quy trình 9 bước để tiế<PERSON> nh<PERSON>n, x<PERSON> lý, và hoàn thiện một kế hoạch (plan) quảng cáo cho khách hàng. Mục tiêu là chuẩn hóa luồng công việc, làm rõ vai trò của từng bộ phận và cung cấp một bản đặc tả chi tiết để xây dựng các kịch bản tự động hóa, gi<PERSON>m thiểu thao tác thủ công và tăng hiệu quả.

Tài liệu này được thiết kế để:

1.  **Hướng dẫn nhân sự mới:** Gi<PERSON><PERSON> nhanh chóng nắm bắt quy trình làm việc.
2.  **<PERSON><PERSON><PERSON> cơ sở cho tự động hóa:** C<PERSON> cấp thông tin đầu vào có cấu trúc cho các công cụ AI và nền tảng low-code/no-code để thiết lập workflow tự động.

## **II. Vai Trò Các Bên Liên Quan**

  * **SALE / ACCOUNT PLAN:**

      * **Vai trò:** Là đầu mối làm việc trực tiếp với khách hàng hoặc agency.
      * **Nhiệm vụ:** Gửi yêu cầu (order) làm plan, cung cấp đầy đủ thông tin ban đầu, đàm phán và chốt plan với khách hàng.

  * **ACCOUNT SERVING:**

      * **Vai trò:** Là người chịu trách nhiệm chính trong việc xử lý plan.
      * **Nhiệm vụ:** Tiếp nhận order, đánh giá thông tin, điều phối với các bộ phận liên quan (Sản phẩm, Inventory), xây dựng plan chi tiết và thực hiện các lần chỉnh sửa (revision).

  * **BỘ PHẬN SẢN PHẨM (BP SẢN PHẨM):**

      * **Vai trò:** Tư vấn chuyên môn về sản phẩm, nội dung và tính khả thi của KPI.
      * **Nhiệm vụ:** Đưa ra góp ý về các hạng mục trong plan, đề xuất nội dung phù hợp, tư vấn về mức chiết khấu và các gói bonus hợp lý.

  * **INVENTORY TEAM:**

      * **Vai trò:** Quản lý và kiểm soát nguồn tài nguyên quảng cáo (tồn kho).
      * **Nhiệm vụ:** Kiểm tra và xác nhận lượng tồn kho (inventory) theo các định dạng (format) quảng cáo, cung cấp định mức và số liệu cần thiết để plan có tính khả thi cao.

-----

## **III. Chi Tiết Quy Trình 9 Bước**

### **Bước 1: NHẬN ORDER PLAN (TỪ SALE/AGENCY)**

  * **Mục tiêu:** Tự động hóa việc tiếp nhận và chuẩn hóa dữ liệu đầu vào ngay khi có yêu cầu mới.
  * **Người thực hiện:** Sale / Agency.
  * **Hệ thống kích hoạt (Trigger):** Một email mới được gửi đến một địa chỉ email được chỉ định (ví dụ: `<EMAIL>`) với tiêu đề hoặc nhãn (label) cụ thể.

#### **Thông tin đầu vào (Input):**

Email từ Sale/Agency phải chứa các thông tin sau:

  * **Thông tin client:** Tên công ty, brand, người liên hệ.
  * **Sản phẩm/Dịch vụ:** Link website, app, hoặc mô tả chi tiết sản phẩm cần quảng cáo.
  * **Mục tiêu truyền thông:** Awareness, Traffic, Lead Generation, Sales, App Install...
  * **Thông tin chiến dịch:**
      * Ngành hàng (VD: FMCG, Bất động sản, Game, E-commerce).
      * Ngân sách tổng (VD: 500,000,000 VND).
      * Thời gian chạy (VD: 01/10/2025 - 31/10/2025).
  * **Target Audience (Đối tượng mục tiêu):** Mô tả chi tiết (tuổi, giới tính, sở thích, khu vực địa lý, hành vi...).
  * **KPI mong muốn:** Cung cấp các chỉ số cụ thể (VD: 10,000,000 Impressions, CTR \> 0.5%, 1,000 Leads, CPL \< 150,000 VND).
  * **Deadline cần trả plan:** Ngày giờ cụ thể.

#### **Gợi ý Tự động hóa (Automation Workflow):**

1.  **Trigger: Gmail → New email received**
      * **Filter:** `label:Order-Plan` HOẶC `subject:("Order Plan" OR "Yêu cầu làm plan")`.
2.  **Node: OpenAI → Extract structured information**
      * **Prompt mẫu:**
        ```
        Trích xuất các thông tin sau từ nội dung email dưới đây và trả về dưới dạng một đối tượng JSON. Nếu thông tin nào không có, hãy để giá trị là null.

        Các trường cần trích xuất:
        - client_name: Tên khách hàng hoặc brand.
        - product_link: Link website hoặc sản phẩm.
        - campaign_goal: Mục tiêu chính của chiến dịch (VD: Awareness, Lead Generation).
        - industry: Ngành hàng.
        - budget: Ngân sách tổng, chỉ lấy số.
        - start_date: Ngày bắt đầu chạy.
        - end_date: Ngày kết thúc chạy.
        - target_audience_summary: Tóm tắt đối tượng mục tiêu.
        - desired_kpis: Liệt kê các KPI mong muốn.
        - response_deadline: Deadline cần trả plan.
        - sender_email: Email của người gửi.

        Nội dung email:
        """
        [Nội dung email sẽ được chèn vào đây]
        """
        ```
3.  **Node: Set → Format Data**
      * Chuyển đổi các định dạng ngày tháng sang chuẩn ISO (YYYY-MM-DD).
      * Làm sạch dữ liệu số (loại bỏ "VND", dấu phẩy).
4.  **Node: Notion/Airtable → Create a new record**
      * Ghi đối tượng JSON đã được xử lý vào bảng `Plan Requests` với các trường tương ứng.
      * Gán trạng thái ban đầu là `New Order`.
5.  **Node: Email → Notify the Leader**
      * **Người nhận:** Email của Leader team Account Serving.
      * **Tiêu đề:** `[New Order] Yêu cầu làm plan cho khách hàng {{client_name}}`.
      * **Nội dung:** "Vừa có một yêu cầu làm plan mới từ {{sender\_email}} cho khách hàng {{client\_name}}. Vui lòng truy cập vào hệ thống để xem chi tiết và phân công người phụ trách."

-----

### **Bước 2: ASSIGN LEADER PHỤ TRÁCH**

  * **Mục tiêu:** Đảm bảo mọi yêu cầu mới đều được phân công người xử lý trong thời gian quy định.
  * **Người thực hiện:** Leader team Account Serving & Account được phân công.

#### **Mô tả quy trình:**

1.  Leader nhận email thông báo.
2.  Trong vòng **30 phút**, Leader truy cập hệ thống (Notion/Airtable/Sheet) và chọn người phụ trách cho yêu cầu đó.
3.  Nếu sau 30 phút mà yêu cầu vẫn ở trạng thái `New Order`, hệ thống tự động gửi email nhắc nhở cho Leader.
4.  Người được phân công (Account) nhận thông báo và xác nhận đã tiếp nhận công việc.

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Notion/Airtable → When a new record is created in `Plan Requests`**
2.  **Node: IF/Router → Check product type/brand**
      * **Logic:** Dựa trên một bảng mapping (có thể lưu trong Google Sheet hoặc Airtable) để tự động đề xuất người phụ trách.
          * `IF industry == "FMCG" THEN assign_to = "<EMAIL>"`
          * `IF client_name == "Big Client X" THEN assign_to = "<EMAIL>"`
          * `ELSE assign_to = "<EMAIL>"`
3.  **Node: Set → Assign 담당자**
      * Cập nhật trường `Assigned To` trong bản ghi Notion/Airtable với email đã xác định ở bước trên.
4.  **Node: Chat/Email → Send Notification**
      * **Gửi tới:** Email/Chat của người được gán.
      * **Nội dung:** "Bạn vừa được phân công phụ trách plan cho khách hàng **{{client\_name}}**. Deadline trả plan là **{{response\_deadline}}**. Vui lòng bấm vào link sau để xác nhận đã nhận việc." (Link có thể là một webhook để cập nhật trạng thái).
5.  **Node: Google Sheets → Update Status**
      * Ghi lại log: `Timestamp`, `Plan ID`, `Assigned To`, và cập nhật trạng thái thành `Assigned`.

-----

### **Bước 3: ACCOUNT TIẾP NHẬN & ĐÁNH GIÁ ĐẦU VÀO**

  * **Mục tiêu:** Đảm bảo Account phụ trách có đầy đủ thông tin cần thiết để bắt đầu làm plan.
  * **Người thực hiện:** Account phụ trách.

#### **Mô tả quy trình:**

Account sau khi nhận việc sẽ kiểm tra toàn bộ thông tin trong order. Nếu thiếu, Account sẽ chủ động yêu cầu Sale/Agency bổ sung.

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Google Sheets/Airtable → When a record's status changes to `Assigned`**
2.  **Node: OpenAI → Compare data with a checklist**
      * **Prompt mẫu:**
        ```
        Dựa trên dữ liệu JSON sau, hãy kiểm tra xem có trường nào bị thiếu thông tin (null hoặc rỗng) so với checklist bắt buộc không.

        Checklist bắt buộc:
        - client_name
        - product_link
        - campaign_goal
        - budget
        - start_date
        - end_date
        - desired_kpis

        Dữ liệu JSON:
        """
        {{Dữ liệu JSON từ bản ghi}}
        """

        Nếu thiếu, hãy trả về một danh sách các trường bị thiếu. Nếu đủ, trả về "Đầy đủ".
        ```
3.  **Node: IF → Check the result from OpenAI**
      * **Condition:** Nếu kết quả từ OpenAI **KHÔNG** phải là "Đầy đủ".
      * **Action (IF TRUE):**
          * **Node: Gmail → Send a templated email**
              * **Người nhận:** {{sender\_email}} (người gửi order ban đầu).
              * **Tiêu đề:** `[Cần Bổ Sung] Thông tin cho plan của khách hàng {{client_name}}`.
              * **Nội dung:** "Chào bạn, tôi là {{account\_name}} phụ trách plan cho {{client\_name}}. Để tiến hành, tôi cần bạn bổ sung các thông tin sau: [Danh sách các trường bị thiếu từ OpenAI]. Cảm ơn bạn."
          * **Node: Google Sheets/Airtable → Update Status**
              * Cập nhật trạng thái của plan thành `Pending Information`.
      * **Action (IF FALSE):**
          * **Node: Google Sheets/Airtable → Update Status**
              * Cập nhật trạng thái của plan thành `Information Sufficient`.

-----

### **Bước 4: KIỂM TRA TỒN KHO & PHÂN BỔ NGÂN SÁCH**

  * **Mục tiêu:** Xây dựng một bản kế hoạch sơ bộ (draft) dựa trên dữ liệu tồn kho thực tế và mục tiêu của khách hàng.
  * **Người thực hiện:** Account phụ trách.

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Google Sheets/Airtable → When a record's status changes to `Information Sufficient`**
2.  **Node: Airtable API → Get inventory data**
      * **Action:** Gửi một API request đến bảng `Inventory` trong Airtable.
      * **Parameters:** Lọc theo `format_type` và `date_range` (dựa trên `start_date` và `end_date` của plan).
3.  **Node: Function/Code → Budget Allocation Logic**
      * Đây là một node tùy chỉnh để thực thi logic nghiệp vụ:
          * Chia ngân sách theo tỷ lệ phần trăm cho từng format quảng cáo (VD: Display 40%, Video 60%).
          * Dựa trên đơn giá (CPM/CPC/CPV) trung bình của ngành và dữ liệu tồn kho, tính toán các chỉ số ước tính (Impressions, Clicks, Views).
          * `Estimated Impressions = (Budget for Format X) / (CPM for Format X) * 1000`.
4.  **Node: OpenAI → Predict KPIs**
      * **Prompt mẫu:**
        ```
        Với một chiến dịch thuộc ngành hàng **{{industry}}**, ngân sách **{{budget}} VND**, chạy trong khoảng thời gian từ **{{start_date}}** đến **{{end_date}}**, mục tiêu là **{{campaign_goal}}**.

        Dưới đây là phân bổ ngân sách và định dạng dự kiến:
        - Format A: {{budget_A}} VND
        - Format B: {{budget_B}} VND

        Dựa trên dữ liệu thị trường, hãy dự đoán các chỉ số KPI sau ở mức "Tiêu chuẩn" và "Tối ưu":
        - Overall CTR (Tỷ lệ nhấp chuột)
        - Overall CVR (Tỷ lệ chuyển đổi) nếu mục tiêu là Sales/Leads.
        - Video View Rate (VVR) nếu có video.

        Trả về kết quả dưới dạng bảng Markdown.
        ```
5.  **Node: Google Sheets → Update Draft Plan**
      * Tạo một tab mới trong file Google Sheet quản lý chung hoặc một file riêng cho plan này.
      * Điền các dữ liệu đã tính toán: Phân bổ ngân sách, KPI ước tính, chi phí...
6.  **Node: Email/Chat → Notify for Review**
      * **Gửi tới:** Leader team Account Serving.
      * **Nội dung:** "Bản plan sơ bộ cho khách hàng **{{client\_name}}** đã được hoàn thành. Vui lòng truy cập [link Google Sheet] để review."

-----

### **Bước 5: PHỐI HỢP VỚI BP SẢN PHẨM & INVENTORY**

  * **Mục tiêu:** Lấy ý kiến chuyên môn từ các bộ phận liên quan để đảm bảo plan có tính khả thi và hấp dẫn.
  * **Người thực hiện:** Account phụ trách.

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Khi bản plan sơ bộ được Leader duyệt (có thể là một checkbox trong Sheet/Airtable).**
2.  **Node: Google Docs API → Create a document from a template**
      * **Action:** Sao chép một file Google Docs mẫu.
      * **Logic:** Tìm và thay thế các placeholder (VD: `{{CLIENT_NAME}}`, `{{BUDGET_TABLE}}`, `{{KPI_TABLE}}`) bằng dữ liệu từ Google Sheet.
3.  **Node: Email → Send for internal review**
      * **Người nhận:** Địa chỉ email của BP Sản phẩm, team Inventory.
      * **Tiêu đề:** `[Review Plan] Góp ý cho kế hoạch của khách hàng {{client_name}}`.
      * **Nội dung:** "Chào các team, vui lòng xem và cho góp ý trực tiếp trên file Google Docs sau cho plan của khách hàng **{{client\_name}}** trước [deadline nội bộ]. Link: [Link Google Docs vừa tạo]."
4.  **Node: Wait → Delay or Wait for Webhook**
      * Chờ trong 24 giờ hoặc chờ tín hiệu (ví dụ: một webhook được kích hoạt khi có comment trong Docs).
5.  **Node: Google Docs API → Get comments**
      * **Action:** Lấy tất cả các comment trong file Google Docs.
      * **Logic:** Tổng hợp các comment và ghi chú lại vào một trường `Internal Feedback` trong bản ghi Notion/Airtable.

-----

### **Bước 6: REVIEW & GÓP Ý PLAN**

  * **Mục tiêu:** Hoàn thiện plan dựa trên các góp ý từ các bộ phận nội bộ.
  * **Người thực hiện:** Account phụ trách.

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Khi có bản ghi mới trong trường `Internal Feedback`** hoặc sau khoảng thời gian `Wait` ở bước 5.
2.  **Node: OpenAI → Suggest revisions**
      * **Prompt mẫu:**
        ```
        Dưới đây là một bản plan quảng cáo và các góp ý từ team nội bộ. Hãy đề xuất các chỉnh sửa cụ thể cho bản plan.

        Bản plan gốc:
        """
        [Dán nội dung plan sơ bộ vào đây]
        """

        Góp ý nội bộ:
        """
        [Dán các comment đã tổng hợp từ Google Docs]
        """

        Ví dụ: Nếu góp ý là "Chiết khấu cho khách hàng này thấp quá, nên tăng thêm 5%", hãy đề xuất cập nhật lại bảng ngân sách với chiết khấu mới và tính lại số tiền thực trả.
        ```
3.  **Node: Google Sheets → Update the plan sheet**
      * Account phụ trách sẽ dựa vào gợi ý của AI và kinh nghiệm để cập nhật lại file Google Sheet.
4.  **Node: Chat/Email → Notify for Final Confirmation**
      * **Gửi tới:** Account phụ trách và Leader.
      * **Nội dung:** "Plan cho **{{client\_name}}** đã được cập nhật theo góp ý. Vui lòng kiểm tra lần cuối và xác nhận bản final để gửi khách hàng."

-----

### **Bước 7: GỬI PLAN CHO KHÁCH & NHẬN PHẢN HỒI**

  * **Mục tiêu:** Gửi plan đã hoàn thiện cho khách hàng và xử lý phản hồi một cách có hệ thống.
  * **Người thực hiện:** Account phụ trách / Sale.

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Khi trạng thái plan trong Sheet/Airtable được đổi thành `Final Internal`**.
2.  **Node: Gmail/Outlook API → Send email to client**
      * **Action:** Gửi một email từ một template đã được soạn sẵn.
      * **Người nhận:** Email của khách hàng (đã lưu từ đầu).
      * **CC:** Sale phụ trách.
      * **Tiêu đề:** `[Proposal] Kế hoạch truyền thông cho {{client_name}}`.
      * **Nội dung:** Template email chuyên nghiệp, đính kèm link (với quyền view-only) hoặc file PDF của plan.
3.  **Node: Wait for response**
      * Chờ phản hồi qua email. Có thể sử dụng các công cụ tích hợp email để trigger khi có email trả lời trong cùng một luồng (thread).
4.  **Node: OpenAI → Summarize feedback**
      * **Prompt mẫu:**
        ```
        Tóm tắt nội dung email phản hồi từ khách hàng dưới đây và highlight các yêu cầu cần điều chỉnh thành các gạch đầu dòng.

        Nội dung email phản hồi:
        """
        [Nội dung email của khách hàng]
        """
        ```
5.  **Node: Google Sheets/Airtable → Log feedback**
      * Ghi lại bản tóm tắt phản hồi vào trường `Client Feedback`.
      * Nếu có yêu cầu điều chỉnh, đổi trạng thái plan thành `Revision Needed`. Nếu khách hàng chốt, đổi thành `Approved`.

-----

### **Bước 8: REVISION & CHỐT PLAN**

  * **Mục tiêu:** Thực hiện các chỉnh sửa theo yêu cầu của khách hàng một cách nhanh chóng và chính xác.
  * **Người thực hiện:** Account phụ trách.

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Khi trạng thái plan là `Revision Needed`**.
2.  **Node: Google Docs API → Compare versions**
      * Sử dụng API để lấy nội dung text của phiên bản trước và các yêu cầu mới. (Bước này nâng cao, có thể bỏ qua và thực hiện thủ công).
3.  **Node: OpenAI → Suggest revised version**
      * **Prompt mẫu:**
        ```
        Khách hàng yêu cầu những thay đổi sau: "[Dán tóm tắt phản hồi của khách vào đây]".
        Dựa trên bản kế hoạch hiện tại dưới đây, hãy viết lại các phần bị ảnh hưởng (ví dụ: bảng ngân sách, KPI) để đáp ứng yêu cầu của khách.

        Bản kế hoạch hiện tại:
        """
        [Dán nội dung plan cần sửa]
        """
        ```
4.  **Node: Chat/Email → Send for final approval**
      * Gửi bản revise cho Sale/Account Plan để xác nhận lại với khách hàng.
5.  **Node: Google Sheets/Airtable → Update Status**
      * Khi khách hàng đã đồng ý, cập nhật trạng thái thành `Approved`.

-----

### **Bước 9: TRẢ KẾT QUẢ & LƯU TRỮ**

  * **Mục tiêu:** Bàn giao plan cuối cùng và lưu trữ tài liệu một cách khoa học để dễ dàng tra cứu sau này.
  * **Người thực hiện:** Account phụ trách.

#### **Gợi ý Tự động hóa:**

1.  **Trigger: Khi trạng thái plan là `Approved`**.
2.  **Node: Google Drive API → Upload final file**
      * **Action:**
          * Tạo một phiên bản PDF của file Google Sheet/Docs.
          * Kiểm tra xem thư mục `Clients/{{CLIENT_NAME}}/Plans/{{YEAR}}` đã tồn tại chưa. Nếu chưa thì tạo mới.
          * Upload file PDF vào thư mục đó với tên chuẩn: `{{CLIENT_NAME}}_Plan_{{YYYYMMDD}}_Final.pdf`.
3.  **Node: Google Sheets/Airtable → Log the final link**
      * Lấy đường dẫn có thể chia sẻ của file đã upload.
      * Ghi đường dẫn này vào trường `Final Plan URL` trong bản ghi tương ứng. Cập nhật trạng thái thành `Completed`.
4.  **Node: Email → Send final plan to all stakeholders**
      * **Người nhận:** Sale, Leader, và các bên liên quan.
      * **Tiêu đề:** `[FINAL PLAN] Kế hoạch đã chốt cho khách hàng {{client_name}}`.
      * **Nội dung:** "Kế hoạch cho khách hàng **{{client\_name}}** đã được chốt. File cuối cùng được lưu tại đây: [Link file từ Google Drive]. Các bộ phận liên quan vui lòng chuẩn bị cho các bước tiếp theo. Cảm ơn team."