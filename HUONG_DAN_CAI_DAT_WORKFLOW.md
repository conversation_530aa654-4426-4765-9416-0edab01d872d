# Hướng dẫn Cài đặt Workflow n8n Xử lý Order Plan

## Tổng quan
Workflow này tự động hóa quy trình 9 bước xử lý order plan quảng cáo, từ việc tiếp nhận email đến hoàn thiện và lưu trữ kết quả.

## Cấu trúc Workflow

### Các <PERSON>:
1. **Gmail Trigger** - Tiếp nhận email order mới
2. **Extract Order Information** - Trích xuất thông tin từ email
3. **Save to Google Sheets** - Lưu dữ liệu vào Google Sheets
4. **Notify Leader** - Thông báo cho leader
5. **Google Sheets Trigger** - <PERSON> dõi thay đổi trạng thái
6. **Check Status and Route** - Đi<PERSON>u hướng xử lý theo trạng thái
7. **Switch** - Phân luồng xử lý
8. **<PERSON><PERSON><PERSON> bước xử lý** (Step 3-9) - X<PERSON> lý từng giai đoạn
9. **Update Google Sheets Status** - Cập nhật trạng thái
10. **Notify Completion** - Thông báo hoàn thành

## Sơ đồ nối node (để tự kiểm tra và nối thủ công nếu thiếu)

Tên node dưới đây khớp 100% với tệp `n8n_order_plan_complete_workflow.json`. Nếu import mà canvas chưa có đường nối, hãy nối theo danh sách sau:

- Gmail Trigger - New Order → Extract Order Information → Save to Google Sheets → Notify Leader
- Google Sheets Trigger - Status Update → Check Status and Route → Switch - Route Processing
- Switch - Route Processing nhánh `step3` → Step 3 - Check Information → Update Google Sheets Status
- Switch - Route Processing nhánh `request_info` → Request Missing Information → Update Google Sheets Status
- Switch - Route Processing nhánh `step4` → Step 4 - Check Inventory & Budget → Update Google Sheets Status
- Switch - Route Processing nhánh `step5` → Step 5 - Notify Product Team → Update Google Sheets Status
- Switch - Route Processing nhánh `step6` → Step 6 - Finalize Plan → Update Google Sheets Status
- Switch - Route Processing nhánh `step7` → Step 7 - Send to Client → Update Google Sheets Status → Step 8 - Process Client Feedback
- Switch - Route Processing nhánh `step8` → Step 8 - Process Client Feedback → Update Google Sheets Status → Step 9 - Final Storage
- Switch - Route Processing nhánh `step9` → Step 9 - Final Storage → Update Google Sheets Status → Notify Completion

Lưu ý:
- Các bước 6, 7, 8, 9 đều có đầu vào từ `Switch - Route Processing` và còn có dây tiếp diễn: Step 6 → Step 7 → Step 8 → Step 9.

## Cài đặt

### 1. Chuẩn bị Google Sheets

Tạo một Google Sheet với cấu trúc như sau:

| A | B | C | D | E | F | G | H | I | J | K | L | M | N | O | P | Q | R | S | T | U | V | W | X |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| orderId | emailId | threadId | sender | subject | receivedAt | status | client | product | budget | duration | kpi | deadline | industry | targetAudience | assignedTo | assignedAt | informationStatus | planDraft | planFinal | clientFeedback | revisionCount | finalApproved | storagePath |

### 2. Cấu hình Environment Variables

Thêm các biến môi trường sau vào n8n:

```bash
PLAN_SHEET_ID=your_google_sheet_id_here
LEADER_EMAIL=<EMAIL>
PRODUCT_TEAM_EMAIL=<EMAIL>
```

### 3. Cấu hình Gmail

1. Tạo label "Order-Plan" trong Gmail
2. Cấu hình Gmail credentials trong n8n
3. Đảm bảo email có label "Order-Plan" sẽ được trigger

### 4. Cấu hình Google Sheets API

1. Bật Google Sheets API trong Google Cloud Console
2. Tạo service account và download JSON key
3. Cấu hình credentials trong n8n
4. Chia sẻ Google Sheet với service account email

## Cách sử dụng

### 1. Gửi Order Plan
Gửi email đến địa chỉ được cấu hình với:
- Subject chứa từ khóa "Order Plan"
- Nội dung chứa thông tin:
  - Client/Khách hàng
  - Sản phẩm/Link web
  - Ngân sách
  - Thời gian chạy
  - KPI mong muốn
  - Deadline
  - Ngành hàng
  - Target audience

### 2. Theo dõi tiến trình
- Kiểm tra Google Sheet để xem trạng thái
- Nhận email thông báo tại mỗi bước
- Cập nhật trạng thái trong Google Sheet để trigger bước tiếp theo

### 3. Các trạng thái chính
- `New Order` - Order mới được tạo
- `Assigned` - Đã gán người phụ trách
- `Information Sufficient` - Thông tin đầy đủ
- `Pending Information` - Chờ bổ sung thông tin
- `Draft Created` - Đã tạo bản nháp
- `Ready for Client` - Sẵn sàng gửi khách hàng
- `Client Feedback` - Chờ phản hồi khách hàng
- `Approved` - Khách hàng đã chốt
- `Completed` - Hoàn thành

## Bổ sung luồng theo quy trình (theo `QUY TRÌNH TIẾP NHẬN VÀ XỬ LÝ PLAN TƯ VẤN.png` và `1 NHẬN ORDER PLAN TỪ SALE.txt`)

### A. Nhắc Leader assign trong 30 phút, sau đó 15 phút/lần đến khi assign

- Vị trí chèn nhánh: sau node `Notify Leader`.
- Các node nên thêm:
  - Google Sheets - Lookup Row (tìm theo `orderId` cột A) → kiểm tra cột `assignedTo`.
  - Function `Is Assigned?` (true nếu `assignedTo` khác rỗng).
  - Wait `Wait 30m Leader`.
  - Gmail `Remind Leader Assign`.
  - Wait `Wait 15m Leader`.
- Cách nối:
  - Notify Leader → Wait 30m Leader → Google Sheets - Lookup Row → Is Assigned?
    - Nếu ĐÃ assign: kết thúc nhánh nhắc.
    - Nếu CHƯA assign: Remind Leader Assign → Wait 15m Leader → Google Sheets - Lookup Row (quay lại) → Is Assigned? (lặp cho đến khi assign).

Gợi ý nội dung email Remind Leader Assign:
```
Subject: [Reminder] Chưa assign người phụ trách cho {{$json.orderId}}
Body: Vui lòng assign người phụ trách cho order {{$json.orderId}} ({{$json.client}}). Hệ thống sẽ tiếp tục nhắc mỗi 15 phút cho tới khi hoàn tất.
```

### B. Account xác nhận tiếp nhận trong 30 phút, sau đó 15 phút/lần đến khi confirm

- Vị trí: khi `status = Assigned` (được phát hiện bởi `Google Sheets Trigger - Status Update`).
- Các node nên thêm:
  - Gmail `Ask Assignee Confirm` (gửi tới `assignedTo`).
  - Webhook `Assignee Confirm` (tuỳ chọn) hoặc cập nhật cột `assignedAt` thủ công/qua API.
  - Wait `Wait 30m Assignee`.
  - Google Sheets - Lookup Row → Function `Is Accepted?` (kiểm tra `assignedAt`).
  - Gmail `Remind Assignee Confirm`.
  - Wait `Wait 15m Assignee`.
- Cách nối:
  - Từ `Switch - Route Processing` nhánh `step3` (trước khi vào `Step 3 - Check Information`) → Ask Assignee Confirm → Wait 30m Assignee → Google Sheets - Lookup Row → Is Accepted?
    - Nếu ĐÃ xác nhận: đi tiếp `Step 3 - Check Information`.
    - Nếu CHƯA xác nhận: Remind Assignee Confirm → Wait 15m Assignee → Google Sheets - Lookup Row (quay lại) → Is Accepted? (lặp cho đến khi xác nhận).

Gợi ý nội dung email Ask Assignee Confirm:
```
Subject: [Confirm] Nhận xử lý order {{$json.orderId}} - {{$json.client}}
Body: Vui lòng xác nhận tiếp nhận order này. Nếu dùng link xác nhận: {{WEBHOOK_CONFIRM_URL}}?orderId={{$json.orderId}}
```

### C. Xử lý đủ/thiếu thông tin sau khi account confirm

- Khi assignee đã xác nhận, chạy `Step 3 - Check Information`:
  - Nếu đủ thông tin: set `status = Information Sufficient` → nhánh `step4` chạy `Step 4 - Check Inventory & Budget`.
  - Nếu thiếu thông tin: chạy `Request Missing Information` reply theo `threadId`, set `informationStatus = Insufficient`, `status = Pending Information`.

### D. Gửi plan, nhận phản hồi và vòng lặp revise

- Step 6 - Finalize Plan → Step 7 - Send to Client.
- Step 7 - Send to Client → Step 8 - Process Client Feedback:
  - Nếu "đồng ý": `status = Approved` → Step 9 - Final Storage.
  - Nếu "yêu cầu chỉnh sửa": `revisionCount += 1`, `status = Information Sufficient` → quay lại `step4` để tính lại (kích hoạt qua `Google Sheets Trigger - Status Update`).
  - Nếu "từ chối": `status = Closed - Not Won` → kết thúc.

### E. Cột dữ liệu trên Google Sheets (tham chiếu)

- `orderId`, `emailId`, `threadId`, `sender`, `subject`, `receivedAt`
- `status`, `informationStatus`, `client`, `product`, `budget`, `duration`, `kpi`, `deadline`, `industry`, `targetAudience`
- `assignedTo`, `assignedAt`, `planDraft`, `planFinal`, `clientFeedback`, `revisionCount`, `finalApproved`, `storagePath`


## Tùy chỉnh

### 1. Thay đổi logic xử lý
Chỉnh sửa các Function node để thay đổi:
- Logic trích xuất thông tin từ email
- Logic kiểm tra tồn kho
- Logic phân bổ ngân sách
- Logic ước tính KPI

### 2. Thay đổi template email
Chỉnh sửa các Gmail node để thay đổi:
- Nội dung email thông báo
- Template email gửi khách hàng
- Template yêu cầu bổ sung thông tin

### 3. Thêm validation
Thêm các Function node để:
- Validate định dạng email
- Kiểm tra quyền truy cập
- Validate dữ liệu đầu vào

## Troubleshooting

### 1. Gmail Trigger không hoạt động
- Kiểm tra label "Order-Plan" đã được tạo
- Kiểm tra Gmail credentials
- Kiểm tra quyền truy cập Gmail API

### 2. Google Sheets không cập nhật
- Kiểm tra Google Sheets credentials
- Kiểm tra quyền truy cập sheet
- Kiểm tra format dữ liệu

### 3. Email không được gửi
- Kiểm tra Gmail credentials
- Kiểm tra địa chỉ email người nhận
- Kiểm tra spam folder

## Lưu ý quan trọng

1. **Backup dữ liệu**: Thường xuyên backup Google Sheet
2. **Monitor logs**: Theo dõi execution logs trong n8n
3. **Test workflow**: Test kỹ trước khi deploy production
4. **Security**: Bảo mật credentials và API keys
5. **Performance**: Monitor performance khi có nhiều order

## Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra logs trong n8n
2. Kiểm tra cấu hình credentials
3. Test từng node riêng lẻ
4. Liên hệ team IT để được hỗ trợ

