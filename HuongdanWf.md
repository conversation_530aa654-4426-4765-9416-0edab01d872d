Chắc chắn rồi\! Vi<PERSON><PERSON> tạo một file JSON cho n8n để import là cách tuyệt vời để nhanh chóng triển khai luồng tự động hóa này.

Dư<PERSON>i đây, tôi sẽ cung cấp cho bạn:

1.  **Những chuẩn bị cần thiết** trước khi import file (cấu trúc Airtable, credentials).
2.  **Gi<PERSON>i thích chi tiết về workflow** và các bước bạn cần tùy chỉnh.
3.  **File JSON hoàn chỉnh** để bạn sao chép và import vào n8n.

-----

### **1. <PERSON><PERSON><PERSON> bị cần thiết**

Workflow này sử dụng Airtable làm cơ sở dữ liệu trung tâm để theo dõi trạng thái của mỗi plan. Bạn cần tạo một Base trên Airtable với cấu trúc sau:

**Base Name:** `Advertising Plans`
**Table Name:** `Plan Requests`

**C<PERSON><PERSON> trường (Fields):**

| Field Name | Field Type | Ghi chú |
| :--- | :--- | :--- |
| `PlanID` | Autonumber | ID tự động tăng |
| `ClientName` | Single line text | Tên khách hàng |
| `Status` | **Single select** | Trạng thái của plan (quan trọng nhất) |
| `ProductLink` | URL | Link sản phẩm |
| `CampaignGoal` | Single line text | Mục tiêu chiến dịch |
| `Industry` | Single line text | Ngành hàng |
| `Budget` | Number (Currency) | Ngân sách |
| `StartDate` | Date | Ngày bắt đầu |
| `EndDate` | Date | Ngày kết thúc |
| `DesiredKPIs` | Long text | Các KPI mong muốn |
| `ResponseDeadline` | Date | Deadline trả plan |
| `SenderEmail` | Email | Email người gửi order |
| `GmailThreadID` | Single line text | **Rất quan trọng**, để reply email |
| `AssignedTo` | Single line text | Tên/Email người phụ trách |
| `InternalFeedback`| Long text | Góp ý của team nội bộ |
| `ClientFeedback` | Long text | Phản hồi của khách hàng |
| `FinalPlanURL` | URL | Link file plan cuối cùng |

**Các lựa chọn cho trường `Status` (bạn cần tạo chính xác các giá trị này):**

  * `New Order`
  * `Assigned`
  * `Pending Information`
  * `Information Sufficient`
  * `Internal Review`
  * `Final Internal`
  * `Waiting for Client`
  * `Client Feedback Received`
  * `Revision Needed`
  * `Approved`
  * `Completed`
  * `Closed - Not Won`

Ngoài ra, bạn cần kết nối credentials (thông tin xác thực) cho các dịch vụ sau trong n8n:

  * **Gmail** (hoặc Email IMAP/SMTP)
  * **OpenAI**
  * **Airtable**
  * **Slack** (hoặc dịch vụ chat khác)
  * **Google Drive**

-----

### **2. Giải thích về Workflow n8n**

Do tính chất phức tạp và có các điểm chờ hành động thủ công (leader assign, khách hàng phản hồi), workflow được chia thành **2 luồng công việc (workflows) riêng biệt nhưng liên kết với nhau qua Airtable** để đạt hiệu quả cao nhất.

  * **Workflow 1: Tiếp nhận Order mới**

      * **Trigger:** Kích hoạt khi có email mới với label `Order-Plan` trên Gmail.
      * **Hành động:** Trích xuất thông tin bằng OpenAI, tạo một bản ghi mới trong Airtable với `Status: New Order`, và thông báo cho Leader qua Slack.

  * **Workflow 2: Xử lý Plan theo Trạng thái**

      * **Trigger:** Kích hoạt khi một bản ghi trong Airtable được **cập nhật**. Nó sẽ theo dõi trường `Status`.
      * **Hành động:** Sử dụng node `Switch` để rẽ nhánh xử lý tùy theo trạng thái mới là gì (`Assigned`, `Final Internal`, `Client Feedback Received`, `Approved`...). Đây là trái tim của toàn bộ quy trình.

### **3. File JSON để Import**

Hãy sao chép toàn bộ nội dung trong khối mã bên dưới, sau đó vào n8n, chọn "Import from File" và dán nội dung này vào.

```json
{
  "name": "Advertising Plan Processing (Main)",
  "nodes": [
    {
      "parameters": {},
      "id": "18f507b9-8c65-42f0-a0f1-8f55734e622b",
      "name": "START - WHEN PLAN STATUS CHANGES",
      "type": "n8n-nodes-base.airtableTrigger",
      "typeVersion": 1,
      "position": [
        420,
        340
      ],
      "credentials": {
        "airtableApi": {
          "id": "REPLACE_WITH_YOUR_AIRTABLE_CREDENTIALS_ID",
          "name": "Airtable account"
        }
      },
      "properties": {
        "authentication": "apiKey",
        "baseId": "REPLACE_WITH_YOUR_BASE_ID",
        "tableId": "REPLACE_WITH_YOUR_TABLE_ID",
        "triggerOn": "update",
        "watchFieldIds": [
          "fldZrqAnb5k4hVqB0"
        ]
      }
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "{{ $json.body.Status.name }}",
              "operation": "equal",
              "value2": "Assigned"
            }
          ]
        }
      },
      "id": "7132a4e2-afb7-4d7a-8b81-d1c905d4df4d",
      "name": "Status is 'Assigned'?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [
        640,
        -100
      ]
    },
    {
      "parameters": {
        "model": "gpt-4-turbo",
        "prompt": "=Dựa trên dữ liệu JSON sau, hãy kiểm tra xem có trường nào bị thiếu thông tin (null hoặc rỗng) so với checklist bắt buộc không.\n\nChecklist bắt buộc:\n- ClientName\n- ProductLink\n- CampaignGoal\n- Budget\n- StartDate\n- EndDate\n- DesiredKPIs\n\nNếu thiếu, hãy trả về một danh sách các trường bị thiếu, phân tách bởi dấu phẩy (ví dụ: Budget,DesiredKPIs). Nếu đủ, trả về chuỗi \"Đầy đủ\".\n\nDữ liệu JSON:\n\"\"\"\n{{ JSON.stringify($json.body) }}\n\"\"\""
      },
      "id": "0225145b-38d7-463e-b873-138383a54779",
      "name": "OpenAI (Check Info)",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        860,
        -100
      ],
      "credentials": {
        "openAiApi": {
          "id": "REPLACE_WITH_YOUR_OPENAI_CREDENTIALS_ID",
          "name": "OpenAI account"
        }
      }
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "{{ $json.choices[0].message.content }}",
              "operation": "equal",
              "value2": "Đầy đủ"
            }
          ]
        }
      },
      "id": "3046f5ad-850f-4886-905c-dfb89233633d",
      "name": "Information Sufficient?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [
        1080,
        -100
      ]
    },
    {
      "parameters": {
        "authentication": "apiKey",
        "baseId": "REPLACE_WITH_YOUR_BASE_ID",
        "tableId": "REPLACE_WITH_YOUR_TABLE_ID",
        "recordId": "={{ $json.body.id }}",
        "fields": {
          "Status": "fldZrqAnb5k4hVqB0"
        },
        "typecast": true,
        "additionalFields": {}
      },
      "id": "37a9fc27-58b2-4d76-b605-6a8492095815",
      "name": "Airtable (Update Status: Info Sufficient)",
      "type": "n8n-nodes-base.airtable",
      "typeVersion": 2.1,
      "position": [
        1300,
        -200
      ],
      "credentials": {
        "airtableApi": {
          "id": "REPLACE_WITH_YOUR_AIRTABLE_CREDENTIALS_ID",
          "name": "Airtable account"
        }
      }
    },
    {
      "parameters": {
        "authentication": "apiKey",
        "baseId": "REPLACE_WITH_YOUR_BASE_ID",
        "tableId": "REPLACE_WITH_YOUR_TABLE_ID",
        "recordId": "={{ $startNode.json.body.id }}",
        "fields": {
          "Status": "fldTzXgPq4w2nKj9O"
        },
        "typecast": true,
        "additionalFields": {}
      },
      "id": "9bb2e118-fd06-444f-bc14-99ce553331b1",
      "name": "Airtable (Update Status: Pending Info)",
      "type": "n8n-nodes-base.airtable",
      "typeVersion": 2.1,
      "position": [
        1520,
        0
      ],
      "credentials": {
        "airtableApi": {
          "id": "REPLACE_WITH_YOUR_AIRTABLE_CREDENTIALS_ID",
          "name": "Airtable account"
        }
      }
    },
    {
      "parameters": {
        "resource": "message",
        "to": "={{ $startNode.json.body.SenderEmail }}",
        "subject": "Re: {{ $startNode.json.body.EmailSubject }}",
        "html": "=Chào bạn,<br><br>\nTôi là {{ $startNode.json.body.AssignedTo }}, người phụ trách plan cho khách hàng <b>{{ $startNode.json.body.ClientName }}</b>.<br><br>\nĐể tiến hành xây dựng kế hoạch chi tiết và hiệu quả nhất, tôi cần bạn hỗ trợ bổ sung các thông tin sau: <b>{{ $node[\"OpenAI (Check Info)\"].json.choices[0].message.content }}</b><br><br>\nVui lòng phản hồi trong luồng email này. Cảm ơn bạn!",
        "options": {
          "threadId": "={{ $startNode.json.body.GmailThreadID }}"
        }
      },
      "id": "99ca30ed-3cd9-49c0-994f-a7978c4a1617",
      "name": "Gmail (Reply to Email)",
      "type": "n8n-nodes-base.gmail",
      "typeVersion": 3.1,
      "position": [
        1300,
        0
      ],
      "credentials": {
        "gmailOAuth2": {
          "id": "REPLACE_WITH_YOUR_GMAIL_CREDENTIALS_ID",
          "name": "Gmail account"
        }
      }
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "{{ $json.body.Status.name }}",
              "operation": "equal",
              "value2": "Final Internal"
            }
          ]
        }
      },
      "id": "2816922d-6062-430c-847e-a74075ecf0a0",
      "name": "Status is 'Final Internal'?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [
        640,
        140
      ]
    },
    {
      "parameters": {
        "resource": "message",
        "to": "={{ $json.body.SenderEmail }}",
        "subject": "[Proposal] Kế hoạch truyền thông cho {{ $json.body.ClientName }}",
        "html": "=Chào bạn,<br><br>\nChúng tôi xin gửi bạn kế hoạch truyền thông chi tiết đã được đội ngũ xây dựng cho <b>{{ $json.body.ClientName }}</b>.<br><br>\nBạn vui lòng xem file đính kèm (hoặc truy cập link sau: [LINK_PLAN]) và cho chúng tôi biết phản hồi nhé.<br><br>\nTrân trọng.",
        "options": {}
      },
      "id": "426f0927-4cbe-44ce-a764-5a219803b879",
      "name": "Gmail (Send to Client)",
      "type": "n8n-nodes-base.gmail",
      "typeVersion": 3.1,
      "position": [
        860,
        140
      ],
      "credentials": {
        "gmailOAuth2": {
          "id": "REPLACE_WITH_YOUR_GMAIL_CREDENTIALS_ID",
          "name": "Gmail account"
        }
      }
    },
    {
      "parameters": {
        "authentication": "apiKey",
        "baseId": "REPLACE_WITH_YOUR_BASE_ID",
        "tableId": "REPLACE_WITH_YOUR_TABLE_ID",
        "recordId": "={{ $json.body.id }}",
        "fields": {
          "Status": "fldvB3p0l4h2jR8qE"
        },
        "typecast": true,
        "additionalFields": {}
      },
      "id": "d0e2e921-6967-466d-97e3-0589a0319803",
      "name": "Airtable (Update Status: Waiting for Client)",
      "type": "n8n-nodes-base.airtable",
      "typeVersion": 2.1,
      "position": [
        1080,
        140
      ],
      "credentials": {
        "airtableApi": {
          "id": "REPLACE_WITH_YOUR_AIRTABLE_CREDENTIALS_ID",
          "name": "Airtable account"
        }
      }
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "{{ $json.body.Status.name }}",
              "operation": "equal",
              "value2": "Client Feedback Received"
            }
          ]
        }
      },
      "id": "e5b8d234-a690-4822-b525-4c6e9323c2a3",
      "name": "Status is 'Client Feedback Received'?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [
        640,
        380
      ]
    },
    {
      "parameters": {
        "model": "gpt-4-turbo",
        "prompt": "=Phân tích phản hồi sau của khách hàng. Phân loại thành một trong ba nhóm: 'Đồng ý', 'Yêu cầu chỉnh sửa', hoặc 'Từ chối'.\n\nNếu là 'Yêu cầu chỉnh sửa', hãy liệt kê các điểm cụ thể cần thay đổi.\nNếu là 'Từ chối', hãy tóm tắt lý do chính.\n\nPhản hồi của khách hàng:\n\"\"\"\n{{ $json.body.ClientFeedback }}\n\"\"\"\n\nTrả lời theo định dạng JSON sau:\n{\n  \"classification\": \"Đồng ý | Yêu cầu chỉnh sửa | Từ chối\",\n  \"summary\": \"Tóm tắt các điểm chính hoặc lý do từ chối\"\n}"
      },
      "id": "8374a2b9-299f-4315-9494-8ab3473de0ac",
      "name": "OpenAI (Summarize & Classify Feedback)",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        860,
        380
      ],
      "credentials": {
        "openAiApi": {
          "id": "REPLACE_WITH_YOUR_OPENAI_CREDENTIALS_ID",
          "name": "OpenAI account"
        }
      }
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{ JSON.parse($json.choices[0].message.content).classification }}",
              "operation": "equal",
              "value2": "Đồng ý"
            }
          ]
        },
        "options": {}
      },
      "id": "31274d6c-68fd-430c-ab23-863a17e0b57e",
      "name": "IF Feedback is 'Approve'",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [
        1080,
        380
      ]
    },
    {
      "parameters": {
        "authentication": "apiKey",
        "baseId": "REPLACE_WITH_YOUR_BASE_ID",
        "tableId": "REPLACE_WITH_YOUR_TABLE_ID",
        "recordId": "={{ $startNode.json.body.id }}",
        "fields": {
          "Status": "fldB7jO8p9l3kR4qA"
        },
        "typecast": true,
        "additionalFields": {}
      },
      "id": "b328a649-659f-4b07-88f5-3c99ab2d480e",
      "name": "Airtable (Update Status: Approved)",
      "type": "n8n-nodes-base.airtable",
      "typeVersion": 2.1,
      "position": [
        1300,
        280
      ],
      "credentials": {
        "airtableApi": {
          "id": "REPLACE_WITH_YOUR_AIRTABLE_CREDENTIALS_ID",
          "name": "Airtable account"
        }
      }
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{ JSON.parse($node[\"OpenAI (Summarize & Classify Feedback)\"].json.choices[0].message.content).classification }}",
              "operation": "equal",
              "value2": "Yêu cầu chỉnh sửa"
            }
          ]
        },
        "options": {}
      },
      "id": "1894d382-7f28-406a-ad85-932f7a957a79",
      "name": "IF Feedback is 'Revise'",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [
        1080,
        480
      ]
    },
    {
      "parameters": {
        "authentication": "apiKey",
        "baseId": "REPLACE_WITH_YOUR_BASE_ID",
        "tableId": "REPLACE_WITH_YOUR_TABLE_ID",
        "recordId": "={{ $startNode.json.body.id }}",
        "fields": {
          "Status": "fldN5mK9oPq2lR4sB"
        },
        "typecast": true,
        "additionalFields": {}
      },
      "id": "278783e4-8f1d-4071-92b6-79c5c4f2bb75",
      "name": "Airtable (Update Status: Revision Needed)",
      "type": "n8n-nodes-base.airtable",
      "typeVersion": 2.1,
      "position": [
        1300,
        480
      ],
      "credentials": {
        "airtableApi": {
          "id": "REPLACE_WITH_YOUR_AIRTABLE_CREDENTIALS_ID",
          "name": "Airtable account"
        }
      }
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{ JSON.parse($node[\"OpenAI (Summarize & Classify Feedback)\"].json.choices[0].message.content).classification }}",
              "operation": "equal",
              "value2": "Từ chối"
            }
          ]
        },
        "options": {}
      },
      "id": "d0473a21-c1e1-4560-84a8-6f5df7a4d53c",
      "name": "IF Feedback is 'Reject'",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [
        1080,
        580
      ]
    },
    {
      "parameters": {
        "authentication": "apiKey",
        "baseId": "REPLACE_WITH_YOUR_BASE_ID",
        "tableId": "REPLACE_WITH_YOUR_TABLE_ID",
        "recordId": "={{ $startNode.json.body.id }}",
        "fields": {
          "Status": "fldX6yZ8p9l3kR4qC"
        },
        "typecast": true,
        "additionalFields": {}
      },
      "id": "f5509787-8c76-4767-8c7c-4a37f0b2f638",
      "name": "Airtable (Update Status: Closed - Not Won)",
      "type": "n8n-nodes-base.airtable",
      "typeVersion": 2.1,
      "position": [
        1300,
        580
      ],
      "credentials": {
        "airtableApi": {
          "id": "REPLACE_WITH_YOUR_AIRTABLE_CREDENTIALS_ID",
          "name": "Airtable account"
        }
      }
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "{{ $json.body.Status.name }}",
              "operation": "equal",
              "value2": "Approved"
            }
          ]
        }
      },
      "id": "185f265b-449e-4e4f-827c-9b0d2d3a3d5e",
      "name": "Status is 'Approved'?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [
        640,
        620
      ]
    },
    {
      "parameters": {
        "authentication": "apiKey",
        "baseId": "REPLACE_WITH_YOUR_BASE_ID",
        "tableId": "REPLACE_WITH_YOUR_TABLE_ID",
        "recordId": "={{ $json.body.id }}",
        "fields": {
          "Status": "fldA9bC8d7e6f5g4h"
        },
        "typecast": true,
        "additionalFields": {}
      },
      "id": "97e64172-1369-450f-90e6-5b4819d2e18d",
      "name": "Airtable (Update Status: Completed)",
      "type": "n8n-nodes-base.airtable",
      "typeVersion": 2.1,
      "position": [
        1260,
        720
      ],
      "credentials": {
        "airtableApi": {
          "id": "REPLACE_WITH_YOUR_AIRTABLE_CREDENTIALS_ID",
          "name": "Airtable account"
        }
      }
    },
    {
      "parameters": {
        "channel": "#general",
        "text": "=✅ Plan cho khách hàng *{{ $startNode.json.body.ClientName }}* đã được chốt và hoàn thành!\n\n🔗 File cuối cùng: {{ $node[\"Airtable (Update Final URL)\"].json.fields.FinalPlanURL }}"
      },
      "id": "8f48e36e-cd1a-4d43-a616-e4142f36f874",
      "name": "Slack (Notify Team of Completion)",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 2,
      "position": [
        1260,
        620
      ],
      "credentials": {
        "slackApi": {
          "id": "REPLACE_WITH_YOUR_SLACK_CREDENTIALS_ID",
          "name": "Slack account"
        }
      }
    },
    {
      "parameters": {
        "filePath": "={{ $startNode.json.body.ClientName }}_Plan_{{ $today.toFormat('yyyyMMdd') }}_Final.pdf",
        "options": {
          "folderId": "REPLACE_WITH_YOUR_GOOGLE_DRIVE_FOLDER_ID"
        }
      },
      "id": "d0ab0d9f-14fc-48dd-a7e8-ed477d9c6e5a",
      "name": "Google Drive (Upload Final Plan)",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 2,
      "position": [
        860,
        620
      ],
      "credentials": {
        "googleDriveOAuth2": {
          "id": "REPLACE_WITH_YOUR_GOOGLE_DRIVE_CREDENTIALS_ID",
          "name": "Google Drive account"
        }
      },
      "notes": "LƯU Ý: Node này cần có file binary để upload. Bạn cần thêm một bước tạo file PDF từ Google Sheet/Docs trước node này. Đây chỉ là node upload placeholder."
    },
    {
      "parameters": {
        "authentication": "apiKey",
        "baseId": "REPLACE_WITH_YOUR_BASE_ID",
        "tableId": "REPLACE_WITH_YOUR_TABLE_ID",
        "recordId": "={{ $startNode.json.body.id }}",
        "fields": {
          "FinalPlanURL": "={{ $node[\"Google Drive (Upload Final Plan)\"].json.webViewLink }}"
        },
        "typecast": true,
        "additionalFields": {}
      },
      "id": "834f3b25-c6aa-4c46-953e-2fefac3f6f96",
      "name": "Airtable (Update Final URL)",
      "type": "n8n-nodes-base.airtable",
      "typeVersion": 2.1,
      "position": [
        1060,
        620
      ],
      "credentials": {
        "airtableApi": {
          "id": "REPLACE_WITH_YOUR_AIRTABLE_CREDENTIALS_ID",
          "name": "Airtable account"
        }
      }
    }
  ],
  "connections": {
    "18f507b9-8c65-42f0-a0f1-8f55734e622b": {
      "main": [
        [
          {
            "node": "7132a4e2-afb7-4d7a-8b81-d1c905d4df4d",
            "type": "main",
            "index": 0
          },
          {
            "node": "2816922d-6062-430c-847e-a74075ecf0a0",
            "type": "main",
            "index": 0
          },
          {
            "node": "e5b8d234-a690-4822-b525-4c6e9323c2a3",
            "type": "main",
            "index": 0
          },
          {
            "node": "185f265b-449e-4e4f-827c-9b0d2d3a3d5e",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "7132a4e2-afb7-4d7a-8b81-d1c905d4df4d": {
      "main": [
        [
          {
            "node": "0225145b-38d7-463e-b873-138383a54779",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "0225145b-38d7-463e-b873-138383a54779": {
      "main": [
        [
          {
            "node": "3046f5ad-850f-4886-905c-dfb89233633d",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "3046f5ad-850f-4886-905c-dfb89233633d": {
      "main": [
        [
          {
            "node": "37a9fc27-58b2-4d76-b605-6a8492095815",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "99ca30ed-3cd9-49c0-994f-a7978c4a1617",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "99ca30ed-3cd9-49c0-994f-a7978c4a1617": {
      "main": [
        [
          {
            "node": "9bb2e118-fd06-444f-bc14-99ce553331b1",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "2816922d-6062-430c-847e-a74075ecf0a0": {
      "main": [
        [
          {
            "node": "426f0927-4cbe-44ce-a764-5a219803b879",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "426f0927-4cbe-44ce-a764-5a219803b879": {
      "main": [
        [
          {
            "node": "d0e2e921-6967-466d-97e3-0589a0319803",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "e5b8d234-a690-4822-b525-4c6e9323c2a3": {
      "main": [
        [
          {
            "node": "8374a2b9-299f-4315-9494-8ab3473de0ac",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "8374a2b9-299f-4315-9494-8ab3473de0ac": {
      "main": [
        [
          {
            "node": "31274d6c-68fd-430c-ab23-863a17e0b57e",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "31274d6c-68fd-430c-ab23-863a17e0b57e": {
      "main": [
        [
          {
            "node": "b328a649-659f-4b07-88f5-3c99ab2d480e",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "1894d382-7f28-406a-ad85-932f7a957a79",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "1894d382-7f28-406a-ad85-932f7a957a79": {
      "main": [
        [
          {
            "node": "278783e4-8f1d-4071-92b6-79c5c4f2bb75",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "d0473a21-c1e1-4560-84a8-6f5df7a4d53c",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "d0473a21-c1e1-4560-84a8-6f5df7a4d53c": {
      "main": [
        [
          {
            "node": "f5509787-8c76-4767-8c7c-4a37f0b2f638",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "185f265b-449e-4e4f-827c-9b0d2d3a3d5e": {
      "main": [
        [
          {
            "node": "d0ab0d9f-14fc-48dd-a7e8-ed477d9c6e5a",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "8f48e36e-cd1a-4d43-a616-e4142f36f874": {
      "main": [
        [
          {
            "node": "97e64172-1369-450f-90e6-5b4819d2e18d",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "d0ab0d9f-14fc-48dd-a7e8-ed477d9c6e5a": {
      "main": [
        [
          {
            "node": "834f3b25-c6aa-4c46-953e-2fefac3f6f96",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "834f3b25-c6aa-4c46-953e-2fefac3f6f96": {
      "main": [
        [
          {
            "node": "8f48e36e-cd1a-4d43-a616-e4142f36f874",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "active": false,
  "settings": {
    "executionOrder": "v1"
  },
  "versionId": "f7d5440d-d49d-48d6-95a9-e85dfc1d4a04",
  "meta": {
    "templateCredsSetupCompleted": true
  },
  "tags": [],
  "id": "J1wOa9gXyM6iQj7r"
}
```

```json
{
  "name": "Advertising Plan Intake (Email)",
  "nodes": [
    {
      "parameters": {
        "options": {
          "folder": "Order-Plan",
          "unseen": true
        }
      },
      "id": "418080f5-ef8b-4965-b1a3-677a2884a475",
      "name": "START - WHEN NEW ORDER EMAIL ARRIVES",
      "type": "n8n-nodes-base.emailReadImap",
      "typeVersion": 1,
      "position": [
        440,
        360
      ],
      "credentials": {
        "imap": {
          "id": "REPLACE_WITH_YOUR_IMAP_CREDENTIALS_ID",
          "name": "IMAP account"
        }
      }
    },
    {
      "parameters": {
        "model": "gpt-4-turbo",
        "prompt": "=Trích xuất các thông tin sau từ nội dung email dưới đây và trả về dưới dạng một đối tượng JSON. Nếu thông tin nào không có, hãy để giá trị là null.\n\nCác trường cần trích xuất:\n- client_name\n- product_link\n- campaign_goal\n- industry\n- budget (chỉ lấy số)\n- start_date (định dạng YYYY-MM-DD)\n- end_date (định dạng YYYY-MM-DD)\n- desired_kpis\n- response_deadline (định dạng YYYY-MM-DD)\n\nNội dung email:\n\"\"\"\n{{ $json.text }}\n\"\"\"\n"
      },
      "id": "e0374e54-5e3e-4076-90f7-111c1d84f88e",
      "name": "OpenAI (Extract Info)",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        660,
        360
      ],
      "credentials": {
        "openAiApi": {
          "id": "REPLACE_WITH_YOUR_OPENAI_CREDENTIALS_ID",
          "name": "OpenAI account"
        }
      }
    },
    {
      "parameters": {
        "values": {
          "string": [
            {
              "name": "parsed_data",
              "value": "={{ JSON.parse($json.choices[0].message.content) }}"
            }
          ]
        },
        "options": {}
      },
      "id": "673752e0-2475-4c07-85b5-68ff773b185b",
      "name": "Set & Format Data",
      "type": "n8n-nodes-base.set",
      "typeVersion": 2,
      "position": [
        880,
        360
      ]
    },
    {
      "parameters": {
        "authentication": "apiKey",
        "baseId": "REPLACE_WITH_YOUR_BASE_ID",
        "tableId": "REPLACE_WITH_YOUR_TABLE_ID",
        "fields": {
          "ClientName": "={{ $node[\"Set & Format Data\"].json.parsed_data.client_name }}",
          "Status": "fldS7tU8vW6xY5z4a",
          "ProductLink": "={{ $node[\"Set & Format Data\"].json.parsed_data.product_link }}",
          "CampaignGoal": "={{ $node[\"Set & Format Data\"].json.parsed_data.campaign_goal }}",
          "Industry": "={{ $node[\"Set & Format Data\"].json.parsed_data.industry }}",
          "Budget": "={{ $node[\"Set & Format Data\"].json.parsed_data.budget }}",
          "StartDate": "={{ $node[\"Set & Format Data\"].json.parsed_data.start_date }}",
          "EndDate": "={{ $node[\"Set & Format Data\"].json.parsed_data.end_date }}",
          "DesiredKPIs": "={{ $node[\"Set & Format Data\"].json.parsed_data.desired_kpis }}",
          "ResponseDeadline": "={{ $node[\"Set & Format Data\"].json.parsed_data.response_deadline }}",
          "SenderEmail": "={{ $trigger.from.email }}",
          "GmailThreadID": "={{ $trigger.threadId }}",
          "EmailSubject": "={{ $trigger.subject }}"
        },
        "typecast": true,
        "additionalFields": {}
      },
      "id": "b0a67e41-6101-4ec4-9457-4144cc795908",
      "name": "Airtable (Create Record)",
      "type": "n8n-nodes-base.airtable",
      "typeVersion": 2.1,
      "position": [
        1100,
        360
      ],
      "credentials": {
        "airtableApi": {
          "id": "REPLACE_WITH_YOUR_AIRTABLE_CREDENTIALS_ID",
          "name": "Airtable account"
        }
      }
    },
    {
      "parameters": {
        "channel": "#planning",
        "text": "=🔔 *New Plan Order!*\n\n*Client:* {{ $json.fields.ClientName }}\n*Budget:* {{ $json.fields.Budget }}\n*Deadline:* {{ $json.fields.ResponseDeadline }}\n\nPlease assign an owner in Airtable."
      },
      "id": "89b9d3b7-789a-4c28-98e8-9759c55b1f89",
      "name": "Slack (Notify Leader)",
      "type": "n8n-nodes-base.slack",
      "typeVersion": 2,
      "position": [
        1320,
        360
      ],
      "credentials": {
        "slackApi": {
          "id": "REPLACE_WITH_YOUR_SLACK_CREDENTIALS_ID",
          "name": "Slack account"
        }
      }
    }
  ],
  "connections": {
    "418080f5-ef8b-4965-b1a3-677a2884a475": {
      "main": [
        [
          {
            "node": "e0374e54-5e3e-4076-90f7-111c1d84f88e",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "e0374e54-5e3e-4076-90f7-111c1d84f88e": {
      "main": [
        [
          {
            "node": "673752e0-2475-4c07-85b5-68ff773b185b",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "673752e0-2475-4c07-85b5-68ff773b185b": {
      "main": [
        [
          {
            "node": "b0a67e41-6101-4ec4-9457-4144cc795908",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "b0a67e41-6101-4ec4-9457-4144cc795908": {
      "main": [
        [
          {
            "node": "89b9d3b7-789a-4c28-98e8-9759c55b1f89",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "active": false,
  "settings": {
    "executionOrder": "v1"
  },
  "versionId": "f05566f1-628d-4f1b-a53d-2490d3d528b8",
  "meta": {
    "templateCredsSetupCompleted": true
  },
  "tags": [],
  "id": "H7aL7yGzQkFm7xU9"
}
```