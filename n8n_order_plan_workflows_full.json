{"name": "Order Plan Workflow Package", "version": 1, "workflows": [{"id": "workflow_1", "name": "Order Plan - Step 1", "nodes": [{"id": "1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1, "parameters": {"triggerOn": "newEmail", "filters": {"label": "Order-Plan"}}, "position": [250, 300]}, {"id": "2", "name": "OpenAI Extract Info", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "parameters": {"operation": "completion", "model": "gpt-4", "prompt": "<PERSON><PERSON><PERSON><PERSON> xuất thông tin từ email..."}, "position": [500, 300]}, {"id": "3", "name": "Function Format Data", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "return items.map(item => { /* format data */ return item; });"}, "position": [750, 300]}, {"id": "4", "name": "Google Sheets - Create Row", "type": "n8n-nodes-base.googleSheets", "typeVersion": 2, "parameters": {"operation": "append", "sheetId": "PLAN_REQUESTS_SHEET_ID"}, "position": [1000, 300]}, {"id": "5", "name": "Gmail Notify Leader", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"subject": "[New Order] <PERSON><PERSON><PERSON> c<PERSON>u làm plan mới", "content": "Có order mới..."}, "position": [1250, 300]}], "connections": {}, "settings": {}, "active": false, "versionId": "v1-step-1"}, {"id": "workflow_2", "name": "Order Plan - Step 2", "nodes": [{"id": "1", "name": "Trigger Step 2", "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "parameters": {"triggerOn": "update"}, "position": [250, 300]}, {"id": "2", "name": "Logic/Router", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// Business logic here"}, "position": [500, 300]}, {"id": "3", "name": "Google Sheets - Update Row", "type": "n8n-nodes-base.googleSheets", "typeVersion": 2, "parameters": {"operation": "update"}, "position": [750, 300]}, {"id": "4", "name": "OpenAI (Optional)", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "parameters": {"operation": "completion", "model": "gpt-4", "prompt": "Sinh gợi ý / tóm tắt..."}, "position": [1000, 300]}, {"id": "5", "name": "Gmail/Slack Notify", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"subject": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON><PERSON><PERSON> lý", "content": "Trạng thái mới..."}, "position": [1250, 300]}], "connections": {}, "settings": {}, "active": false, "versionId": "v1-step-2"}, {"id": "workflow_3", "name": "Order Plan - Step 3", "nodes": [{"id": "1", "name": "Trigger Step 3", "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "parameters": {"triggerOn": "update"}, "position": [250, 300]}, {"id": "2", "name": "Logic/Router", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// Business logic here"}, "position": [500, 300]}, {"id": "3", "name": "Google Sheets - Update Row", "type": "n8n-nodes-base.googleSheets", "typeVersion": 2, "parameters": {"operation": "update"}, "position": [750, 300]}, {"id": "4", "name": "OpenAI (Optional)", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "parameters": {"operation": "completion", "model": "gpt-4", "prompt": "Sinh gợi ý / tóm tắt..."}, "position": [1000, 300]}, {"id": "5", "name": "Gmail/Slack Notify", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"subject": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON><PERSON><PERSON> lý", "content": "Trạng thái mới..."}, "position": [1250, 300]}], "connections": {}, "settings": {}, "active": false, "versionId": "v1-step-3"}, {"id": "workflow_4", "name": "Order Plan - Step 4", "nodes": [{"id": "1", "name": "Trigger Step 4", "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "parameters": {"triggerOn": "update"}, "position": [250, 300]}, {"id": "2", "name": "Logic/Router", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// Business logic here"}, "position": [500, 300]}, {"id": "3", "name": "Google Sheets - Update Row", "type": "n8n-nodes-base.googleSheets", "typeVersion": 2, "parameters": {"operation": "update"}, "position": [750, 300]}, {"id": "4", "name": "OpenAI (Optional)", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "parameters": {"operation": "completion", "model": "gpt-4", "prompt": "Sinh gợi ý / tóm tắt..."}, "position": [1000, 300]}, {"id": "5", "name": "Gmail/Slack Notify", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"subject": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON><PERSON><PERSON> lý", "content": "Trạng thái mới..."}, "position": [1250, 300]}], "connections": {}, "settings": {}, "active": false, "versionId": "v1-step-4"}, {"id": "workflow_5", "name": "Order Plan - Step 5", "nodes": [{"id": "1", "name": "Trigger Step 5", "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "parameters": {"triggerOn": "update"}, "position": [250, 300]}, {"id": "2", "name": "Logic/Router", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// Business logic here"}, "position": [500, 300]}, {"id": "3", "name": "Google Sheets - Update Row", "type": "n8n-nodes-base.googleSheets", "typeVersion": 2, "parameters": {"operation": "update"}, "position": [750, 300]}, {"id": "4", "name": "OpenAI (Optional)", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "parameters": {"operation": "completion", "model": "gpt-4", "prompt": "Sinh gợi ý / tóm tắt..."}, "position": [1000, 300]}, {"id": "5", "name": "Gmail/Slack Notify", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"subject": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON><PERSON><PERSON> lý", "content": "Trạng thái mới..."}, "position": [1250, 300]}], "connections": {}, "settings": {}, "active": false, "versionId": "v1-step-5"}, {"id": "workflow_6", "name": "Order Plan - Step 6", "nodes": [{"id": "1", "name": "Trigger Step 6", "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "parameters": {"triggerOn": "update"}, "position": [250, 300]}, {"id": "2", "name": "Logic/Router", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// Business logic here"}, "position": [500, 300]}, {"id": "3", "name": "Google Sheets - Update Row", "type": "n8n-nodes-base.googleSheets", "typeVersion": 2, "parameters": {"operation": "update"}, "position": [750, 300]}, {"id": "4", "name": "OpenAI (Optional)", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "parameters": {"operation": "completion", "model": "gpt-4", "prompt": "Sinh gợi ý / tóm tắt..."}, "position": [1000, 300]}, {"id": "5", "name": "Gmail/Slack Notify", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"subject": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON><PERSON><PERSON> lý", "content": "Trạng thái mới..."}, "position": [1250, 300]}], "connections": {}, "settings": {}, "active": false, "versionId": "v1-step-6"}, {"id": "workflow_7", "name": "Order Plan - Step 7", "nodes": [{"id": "1", "name": "Trigger Step 7", "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "parameters": {"triggerOn": "update"}, "position": [250, 300]}, {"id": "2", "name": "Logic/Router", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// Business logic here"}, "position": [500, 300]}, {"id": "3", "name": "Google Sheets - Update Row", "type": "n8n-nodes-base.googleSheets", "typeVersion": 2, "parameters": {"operation": "update"}, "position": [750, 300]}, {"id": "4", "name": "OpenAI (Optional)", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "parameters": {"operation": "completion", "model": "gpt-4", "prompt": "Sinh gợi ý / tóm tắt..."}, "position": [1000, 300]}, {"id": "5", "name": "Gmail/Slack Notify", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"subject": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON><PERSON><PERSON> lý", "content": "Trạng thái mới..."}, "position": [1250, 300]}], "connections": {}, "settings": {}, "active": false, "versionId": "v1-step-7"}, {"id": "workflow_8", "name": "Order Plan - Step 8", "nodes": [{"id": "1", "name": "Trigger Step 8", "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "parameters": {"triggerOn": "update"}, "position": [250, 300]}, {"id": "2", "name": "Logic/Router", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// Business logic here"}, "position": [500, 300]}, {"id": "3", "name": "Google Sheets - Update Row", "type": "n8n-nodes-base.googleSheets", "typeVersion": 2, "parameters": {"operation": "update"}, "position": [750, 300]}, {"id": "4", "name": "OpenAI (Optional)", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "parameters": {"operation": "completion", "model": "gpt-4", "prompt": "Sinh gợi ý / tóm tắt..."}, "position": [1000, 300]}, {"id": "5", "name": "Gmail/Slack Notify", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"subject": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON><PERSON><PERSON> lý", "content": "Trạng thái mới..."}, "position": [1250, 300]}], "connections": {}, "settings": {}, "active": false, "versionId": "v1-step-8"}, {"id": "workflow_9", "name": "Order Plan - Step 9", "nodes": [{"id": "1", "name": "Trigger Step 9", "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "parameters": {"triggerOn": "update"}, "position": [250, 300]}, {"id": "2", "name": "Logic/Router", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "// Business logic here"}, "position": [500, 300]}, {"id": "3", "name": "Google Sheets - Update Row", "type": "n8n-nodes-base.googleSheets", "typeVersion": 2, "parameters": {"operation": "update"}, "position": [750, 300]}, {"id": "4", "name": "OpenAI (Optional)", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "parameters": {"operation": "completion", "model": "gpt-4", "prompt": "Sinh gợi ý / tóm tắt..."}, "position": [1000, 300]}, {"id": "5", "name": "Gmail/Slack Notify", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"subject": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON><PERSON><PERSON> lý", "content": "Trạng thái mới..."}, "position": [1250, 300]}], "connections": {}, "settings": {}, "active": false, "versionId": "v1-step-9"}]}