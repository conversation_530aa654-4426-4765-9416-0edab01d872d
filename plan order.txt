 
 	 
  
1. NHẬN ORDER PLAN (TỪ SALE/AGENCY)
Người gửi: Sale Account Plan khách hàng (trực tiếp hoặc agency)

G<PERSON>i thông tin qua email/chat/ticket Adwork, bao gồm:
Thông tin client
Link web hoặc sản phẩm chạy
Mục tiêu truyền thông
<PERSON>, s<PERSON><PERSON> ph<PERSON>m, ngân sách, thời gian chạy
Target audience
KPI mong muốn
Deadline cần trả

Trigger: Gmail → New email received (filter theo subject hoặc label “Order Plan”)

Node: OpenAI → Extract thông tin có cấu trúc (prompt: "Tr<PERSON><PERSON> xu<PERSON>t ngành hàng, ngân sách, thờ<PERSON> gian ch<PERSON>, <PERSON><PERSON>… từ email này")

Node: Set → Format dữ liệu thành JSON chuẩn

Node: Notion/Airtable → Ghi vào bảng Plan Requests

Node: /Email → Notify người phụ trách review dữ liệu
 
 	 
  
GHI NHỚ VAI TRÒ CÁC BÊN:

SALE/ACCOUNT PLAN: Gửi order, làm vi<PERSON><PERSON> với kh<PERSON>ch hàng.

ACCOUNT SERVING: <PERSON><PERSON><PERSON><PERSON>h<PERSON>n, x<PERSON> lý ch<PERSON>h và phối hợp các bên.

BP SẢN PHẨM: Đưa ý kiến về nội dung, KPI, chiết khấu.

INVENTORY: Kiểm tra tồn kho, định mức.

 
 	 
  
2. ASSIGN LEADER PHỤ TRÁCH
 
 	 
  
a. Leader team nhận email và assign:
Trong vòng 30 phút từ khi order: Leader tiếp nhận và assign người phụ trách (qua mail/chat).
Nếu quá 30 phút chưa ai xử lý → hệ thống gửi mail nhắc leader.

b. Account phụ trách nhận và kiểm tra thông tin:
Kiểm tra các yếu tố:
Format chạy
CF (Creative Format) chính xác chưa
Deadline order

Gửi yêu cầu bổ sung nếu thiếu.

Trigger: Khi có bản ghi mới trong bảng Plan Requests

Node: IF → Check loại sản phẩm/brand

Node: Set → Gán tên người phụ trách tương ứng theo bảng mapping

Node:  → Gửi notification “Bạn được gán vào plan X – bấm xác nhận nhận việc”

Node: Google Sheet → Ghi trạng thái đã assign

 
 	 
  
3. ACCOUNT TIẾP NHẬN & ĐÁNH GIÁ ĐẦU VÀO

 
 	 
  
Nếu thông tin chưa đủ:
Gửi yêu cầu bổ sung cho sale/AC/CM.
Check format, site hợp tác → gửi inventory nếu cần.
Check định mức, lượng hàng.

Trigger: Bản ghi mới có trạng thái “Đã assign”

Node: OpenAI → So sánh dữ liệu với checklist mẫu (prompt check thiếu mục nào)

Node: IF → Nếu thiếu → gửi email mẫu cho Sale nhờ bổ sung

Node: Google Sheet → Update trạng thái “Thiếu thông tin” hoặc “Đầy đủ”

 
 	 
  
4. KIỂM TRA TỒN KHO & PHÂN BỔ NGÂN SÁCH

 
 	 
  
Xác định đúng định dạng (format) chạy
Check tồn kho inventory
Phân bổ lượng theo từng format
Ước lượng chi phí theo format bidding
Estimate KPI theo từng hạng mục
Chia ngân sách theo từng thời điểm
Check chiết khấu và bonus

Trigger: Khi trạng thái là “Đầy đủ”

Node: Airtable API → Gọi dữ liệu tồn kho theo thời gian chạy

Node: Function → Logic chia ngân sách theo format, CPM/CPC, thời gian

Node: OpenAI → Dự đoán KPI theo ngành và budget

Node: Google Sheet → Cập nhật bảng ngân sách + KPI sơ bộ

Node: Email/Lotus → Notify người duyệt plan sơ bộ

 
 	 
  
5. PHỐI HỢP VỚI BP SẢN PHẨM & INVENTORY
 
 	 
  
Nếu cần:
Gửi yêu cầu check inventory và chi tiết đến team INVENTORY
Gửi yêu cầu review plan đến bộ phận sản phẩm

Trigger: Plan sơ bộ đã có

Node: Google Docs API → Tạo file Google Docs từ template + dữ liệu vừa tính

Node: Email → Gửi Docs tới các đầu mối sản phẩm và inventory

Node: Wait → Delay 24h hoặc đến khi có phản hồi

Node: Google Docs API → Lấy comment và hợp nhất về bảng chính
 
 	 
  
6. REVIEW & GÓP Ý PLAN
Sau khi inventory và BP sản phẩm phản hồi, account hoàn thiện lại plan
Review & chỉnh sửa:
Hạng mục sản phẩm
Tỉ lệ inventory
KPI
Chiết khấu, bonus (nếu có) 	 
  
Nếu cần:
Gửi yêu cầu check inventory và chi tiết đến team INVENTORY
Gửi yêu cầu review plan đến bộ phận sản phẩm

Trigger: Có phản hồi góp ý từ Doc

Node: OpenAI → Gợi ý chỉnh sửa nội dung plan dựa trên góp ý (prompt dạng: “Khách muốn giảm ngân sách tuần 1, viết lại bản ngân sách phù hợp hơn”)

Node: Google Sheet → Cập nhật lại bảng kế hoạch

Node:  → Notify người chỉnh sửa & xác nhận bản final

 
 	 
  
7. GỬI PLAN CHO KHÁCH & NHẬN PHẢN HỒI

 
 	 
  
Gửi plan cho khách hàng → khách review:
Nếu khách không chốt → revise theo yêu cầu (thay đổi định mức, format, ngân sách...)
Nếu chốt → note phản hồi & lý do nếu khách không triển khai

Trigger: Khi plan đã xác nhận final

Node: Gmail/Outlook API → Gửi email khách hàng từ template sẵn

Node: Wait for response (email hoặc webhook tích hợp inbox)

Node: OpenAI → Tóm tắt phản hồi khách & highlight các yêu cầu điều chỉnh

Node: Google Sheet → Ghi chú phản hồi + đẩy sang bước revise nếu có

 
 	 
  
8. REVISION & CHỐT PLAN
 	 
Gửi kết quả final cho người order và follow lại
Gắn link lưu kết quả (Google Sheet)

Trigger: Có phản hồi yêu cầu chỉnh sửa

Node: Google Docs API → So sánh version trước và gợi ý mới

Node: OpenAI → Gợi ý bản revise (prompt: "Khách yêu cầu A, hãy viết lại phần ngân sách cho phù hợp")

Node: Lotus/Email → Gửi cho người phụ trách xác nhận và chốt

Node: Google Sheet → Update trạng thái “Đã chốt”

 
more rows at the bottom
 	 
  
9. TRẢ KẾT QUẢ & LƯU TRỮ

Trigger: Plan đã chốt

Node: Google Drive API → Upload file lên thư mục theo khách hàng

Node: Google Sheet → Ghi log đường dẫn + thời gian gửi

Node: Email → Gửi cho sale & các bên liên quan

