{"name": "Order Plan Processing Workflow - Optimized v2.0", "version": 2, "nodes": [{"id": "1", "name": "<PERSON><PERSON> - New Order", "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 2, "parameters": {"triggerOn": "newEmail", "filters": {"labelIds": ["Order-Plan"], "includeSpamTrash": false}, "pollTimes": {"item": [{"mode": "everyMinute"}]}}, "position": [250, 300], "webhookId": "gmail-trigger"}, {"id": "2", "name": "OpenAI - Extract Order Information", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "parameters": {"operation": "chat", "model": "gpt-4o-mini", "messages": {"values": [{"role": "system", "content": "<PERSON><PERSON><PERSON> l<PERSON> một AI assistant ch<PERSON><PERSON><PERSON> tr<PERSON><PERSON> xuất thông tin từ email order plan. Tr<PERSON>ch xuất thông tin và trả về JSON với các trường: client_name, product_link, campaign_goal, industry, budget (chỉ số), start_date, end_date, target_audience, desired_kpis, deadline, sender_email. Nếu không có thông tin, để giá trị null."}, {"role": "user", "content": "Email subject: {{$json.subject}}\nEmail content: {{$json.textPlain || $json.textHtml}}\nSender: {{$json.from}}"}]}, "options": {"temperature": 0.1, "maxTokens": 1000}}, "position": [500, 300]}, {"id": "2b", "name": "Process Extracted Data", "type": "n8n-nodes-base.function", "typeVersion": 2, "parameters": {"functionCode": "try {\n  const email = $input.first().json;\n  const aiResponse = JSON.parse($input.first().json.choices[0].message.content);\n  \n  const orderId = 'ORD-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);\n  \n  const orderData = {\n    orderId: orderId,\n    emailId: email.id,\n    threadId: email.threadId,\n    sender: aiResponse.sender_email || email.from,\n    subject: email.subject,\n    receivedAt: new Date().toISOString(),\n    status: 'New Order',\n    client: aiResponse.client_name || '',\n    product: aiResponse.product_link || '',\n    campaignGoal: aiResponse.campaign_goal || '',\n    budget: aiResponse.budget || '',\n    startDate: aiResponse.start_date || '',\n    endDate: aiResponse.end_date || '',\n    kpi: aiResponse.desired_kpis || '',\n    deadline: aiResponse.deadline || '',\n    industry: aiResponse.industry || '',\n    targetAudience: aiResponse.target_audience || '',\n    rawEmail: email.textPlain || email.textHtml,\n    assignedTo: '',\n    assignedAt: '',\n    informationStatus: 'Pending Review',\n    informationRequestCount: 0,\n    planDraft: '',\n    planFinal: '',\n    clientFeedback: '',\n    revisionCount: 0,\n    maxRevisions: 3,\n    finalApproved: false,\n    storagePath: '',\n    leaderNotified: true,\n    assigneeConfirmed: false,\n    lastActivity: new Date().toISOString(),\n    priority: 'Normal',\n    escalated: false\n  };\n  \n  return [{ json: orderData }];\n} catch (error) {\n  return [{ json: { error: 'Failed to process extracted data: ' + error.message } }];\n}"}, "position": [750, 300]}, {"id": "3", "name": "Save to Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 5, "parameters": {"operation": "append", "documentId": "{{$env.PLAN_SHEET_ID}}", "sheetName": "Plan Requests", "columns": {"mappingMode": "defineBelow", "value": {"orderId": "={{$json.orderId}}", "emailId": "={{$json.emailId}}", "threadId": "={{$json.threadId}}", "sender": "={{$json.sender}}", "subject": "={{$json.subject}}", "receivedAt": "={{$json.receivedAt}}", "status": "={{$json.status}}", "client": "={{$json.client}}", "product": "={{$json.product}}", "campaignGoal": "={{$json.campaignGoal}}", "budget": "={{$json.budget}}", "startDate": "={{$json.startDate}}", "endDate": "={{$json.endDate}}", "kpi": "={{$json.kpi}}", "deadline": "={{$json.deadline}}", "industry": "={{$json.industry}}", "targetAudience": "={{$json.targetAudience}}", "assignedTo": "={{$json.assignedTo}}", "assignedAt": "={{$json.assignedAt}}", "informationStatus": "={{$json.informationStatus}}", "informationRequestCount": "={{$json.informationRequestCount}}", "planDraft": "={{$json.planDraft}}", "planFinal": "={{$json.planFinal}}", "clientFeedback": "={{$json.clientFeedback}}", "revisionCount": "={{$json.revisionCount}}", "maxRevisions": "={{$json.maxRevisions}}", "finalApproved": "={{$json.finalApproved}}", "storagePath": "={{$json.storagePath}}", "lastActivity": "={{$json.lastActivity}}", "priority": "={{$json.priority}}", "escalated": "={{$json.escalated}}"}}, "options": {"cellFormat": "USER_ENTERED"}}, "position": [1000, 300]}, {"id": "4", "name": "Notify Leader Initial", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "parameters": {"operation": "send", "to": "{{$env.LEADER_EMAIL}}", "subject": "[🚨 URGENT] New Order Plan - {{$json.client}}", "message": "Chào Leader,\\n\\nCó một order plan mới cần đư<PERSON> xử lý:\\n\\n📋 **Chi tiết Order:**\\n- Order ID: {{$json.orderId}}\\n- Khách hàng: {{$json.client}}\\n- Sản phẩm: {{$json.product}}\\n- Mục tiêu: {{$json.campaignGoal}}\\n- Ngân sách: {{$json.budget}}\\n- Thời gian: {{$json.startDate}} - {{$json.endDate}}\\n- Deadline: {{$json.deadline}}\\n- Thời gian nhận: {{$json.receivedAt}}\\n\\n⏰ **Hành động cần thiết:**\\nVui lòng truy cập Google Sheets để assign người phụ trách trong vòng 30 phút.\\n\\n🔗 **Link Google Sheets:** {{$env.PLAN_SHEET_URL}}\\n\\nTrân trọng,\\nH<PERSON> thống tự động", "options": {"allowUnauthorizedCerts": false}}, "position": [1250, 300]}, {"id": "5", "name": "Wait 30m for Leader", "type": "n8n-nodes-base.wait", "typeVersion": 1, "parameters": {"amount": 30, "unit": "minutes"}, "position": [1250, 300]}, {"id": "6", "name": "Check If Assigned", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "parameters": {"operation": "read", "documentId": "{{$env.PLAN_SHEET_ID}}", "sheetName": "Plan Requests", "filters": {"conditions": [{"column": "orderId", "condition": "equal", "value": "={{$json.orderId}}"}]}}, "position": [1500, 300]}, {"id": "7", "name": "Is Leader Assigned?", "type": "n8n-nodes-base.if", "typeVersion": 2, "parameters": {"conditions": {"string": [{"value1": "={{$json.assignedTo}}", "operation": "isEmpty"}]}}, "position": [1750, 300]}, {"id": "8", "name": "Remind Leader", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "{{$env.LEADER_EMAIL}}", "subject": "[REMINDER] Chưa assign người phụ trách cho {{$json.orderId}}", "message": "<PERSON><PERSON> assign người phụ trách cho order {{$json.orderId}} ({{$json.client}}). <PERSON><PERSON> thống sẽ tiếp tục nhắc mỗi 15 phút cho tới khi hoàn tất."}, "position": [2000, 200]}, {"id": "9", "name": "Wait 15m <PERSON>minder", "type": "n8n-nodes-base.wait", "typeVersion": 1, "parameters": {"amount": 15, "unit": "minutes"}, "position": [2250, 200]}, {"id": "10", "name": "Google Sheets Trigger - Status Update", "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "parameters": {"documentId": "{{$env.PLAN_SHEET_ID}}", "sheetName": "Plan Requests", "triggerOn": "update"}, "position": [250, 800], "webhookId": "sheets-trigger"}, {"id": "11", "name": "Check Status and Route", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "const data = $input.first().json;\nconst status = data.status || '';\nconst assignedTo = data.assignedTo || '';\nconst assigneeConfirmed = data.assigneeConfirmed || false;\nconst informationStatus = data.informationStatus || '';\n\nlet nextStep = 'unknown';\n\n// <PERSON><PERSON><PERSON> tra nếu vừa được assign\nif (status === 'New Order' && assignedTo && !assigneeConfirmed) {\n  nextStep = 'ask_assignee_confirm';\n}\n// Nếu assignee đã confirm\nelse if (assigneeConfirmed && informationStatus === 'Pending Review') {\n  nextStep = 'check_information';\n}\n// <PERSON>ác trạng thái khác\nelse if (informationStatus === 'Insufficient') {\n  nextStep = 'request_info';\n}\nelse if (status === 'Information Sufficient') {\n  nextStep = 'check_inventory';\n}\nelse if (status === 'Draft Created') {\n  nextStep = 'notify_product_team';\n}\nelse if (status === 'Internal Review Complete') {\n  nextStep = 'finalize_plan';\n}\nelse if (status === 'Ready for Client') {\n  nextStep = 'send_to_client';\n}\nelse if (status === 'Client Feedback') {\n  nextStep = 'process_feedback';\n}\nelse if (status === 'Approved') {\n  nextStep = 'final_storage';\n}\n\nreturn [{ json: { ...data, nextStep } }];"}, "position": [500, 800]}, {"id": "12", "name": "Route Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "parameters": {"options": {"fallbackOutput": 0}, "rules": {"values": [{"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "ask_assignee_confirm"}}, "renameOutput": true, "outputKey": "ask_confirm"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "check_information"}}, "renameOutput": true, "outputKey": "check_info"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "request_info"}}, "renameOutput": true, "outputKey": "request_info"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "check_inventory"}}, "renameOutput": true, "outputKey": "inventory"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "notify_product_team"}}, "renameOutput": true, "outputKey": "product"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "finalize_plan"}}, "renameOutput": true, "outputKey": "finalize"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "send_to_client"}}, "renameOutput": true, "outputKey": "send_client"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "process_feedback"}}, "renameOutput": true, "outputKey": "feedback"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "final_storage"}}, "renameOutput": true, "outputKey": "storage"}]}}, "position": [750, 800]}, {"id": "13", "name": "Ask Assignee Confirm", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "={{$json.assignedTo}}", "subject": "[CONFIRM] Nhận xử lý order {{$json.orderId}} - {{$json.client}}", "message": "<PERSON><PERSON><PERSON> b<PERSON>,\\n\\nB<PERSON><PERSON> assign xử lý order plan sau:\\n\\n- Order ID: {{$json.orderId}}\\n- Khách hàng: {{$json.client}}\\n- Sản phẩm: {{$json.product}}\\n- Ngân sách: {{$json.budget}}\\n- Deadline: {{$json.deadline}}\\n\\nVui lòng reply email này hoặc cập nhật cột 'assigneeConfirmed' = TRUE trong Google Sheet để xác nhận tiếp nhận.\\n\\nTrân trọng,\\nHệ thống tự động"}, "position": [1000, 600]}, {"id": "14", "name": "Wait 30m for <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.wait", "typeVersion": 1, "parameters": {"amount": 30, "unit": "minutes"}, "position": [1250, 600]}, {"id": "15", "name": "Check Assignee Confirmed", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "parameters": {"operation": "read", "documentId": "{{$env.PLAN_SHEET_ID}}", "sheetName": "Plan Requests", "filters": {"conditions": [{"column": "orderId", "condition": "equal", "value": "={{$json.orderId}}"}]}}, "position": [1500, 600]}, {"id": "16", "name": "Is As<PERSON>ee Confirmed?", "type": "n8n-nodes-base.if", "typeVersion": 2, "parameters": {"conditions": {"boolean": [{"value1": "={{$json.assigneeConfirmed}}", "operation": "equal", "value2": false}]}}, "position": [1750, 600]}, {"id": "17", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "={{$json.assignedTo}}", "subject": "[REMINDER] <PERSON><PERSON><PERSON> nhận tiếp nhận order {{$json.orderId}}", "message": "Bạn chưa xác nhận tiếp nhận order {{$json.orderId}} ({{$json.client}}). <PERSON><PERSON> lòng xác nhận sớm để có thể tiến hành xử lý."}, "position": [2000, 500]}, {"id": "18", "name": "Wait 15m <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.wait", "typeVersion": 1, "parameters": {"amount": 15, "unit": "minutes"}, "position": [2250, 500]}, {"id": "19", "name": "Check Information Complete - Enhanced", "type": "n8n-nodes-base.function", "typeVersion": 2, "parameters": {"functionCode": "try {\n  const data = $input.first().json;\n  \n  // <PERSON><PERSON><PERSON> nghĩa các trư<PERSON> bắt buộc với độ ưu tiên\n  const criticalFields = ['client', 'budget', 'deadline'];\n  const importantFields = ['product', 'campaignGoal', 'industry', 'targetAudience'];\n  const optionalFields = ['startDate', 'endDate', 'kpi'];\n  \n  const missingCritical = [];\n  const missingImportant = [];\n  const missingOptional = [];\n  \n  // Kiểm tra các trường critical\n  for (const field of criticalFields) {\n    if (!data[field] || data[field].toString().trim() === '' || data[field] === null) {\n      missingCritical.push(field);\n    }\n  }\n  \n  // Kiểm tra các trường important\n  for (const field of importantFields) {\n    if (!data[field] || data[field].toString().trim() === '' || data[field] === null) {\n      missingImportant.push(field);\n    }\n  }\n  \n  // <PERSON><PERSON><PERSON> tra các trường optional\n  for (const field of optionalFields) {\n    if (!data[field] || data[field].toString().trim() === '' || data[field] === null) {\n      missingOptional.push(field);\n    }\n  }\n  \n  let newStatus, newInformationStatus, priority;\n  \n  if (missingCritical.length === 0 && missingImportant.length === 0) {\n    newStatus = 'Information Sufficient';\n    newInformationStatus = 'Complete';\n    priority = 'Normal';\n  } else if (missingCritical.length > 0) {\n    newStatus = 'Pending Information';\n    newInformationStatus = 'Critical Missing';\n    priority = 'High';\n  } else {\n    newStatus = 'Pending Information';\n    newInformationStatus = 'Important Missing';\n    priority = 'Medium';\n  }\n  \n  const requestCount = (data.informationRequestCount || 0);\n  const shouldEscalate = requestCount >= 2;\n  \n  return [{ json: {\n    ...data,\n    status: newStatus,\n    informationStatus: newInformationStatus,\n    missingCritical: missingCritical,\n    missingImportant: missingImportant,\n    missingOptional: missingOptional,\n    priority: priority,\n    shouldEscalate: shouldEscalate,\n    lastActivity: new Date().toISOString()\n  }}];\n} catch (error) {\n  return [{ json: { error: 'Failed to check information: ' + error.message } }];\n}"}, "position": [1000, 700]}, {"id": "20", "name": "Request Missing Information - Enhanced", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "parameters": {"operation": "send", "to": "={{$json.sender}}", "cc": "={{$json.shouldEscalate ? $env.LEADER_EMAIL : ''}}", "subject": "{{$json.shouldEscalate ? '[ESCALATED] ' : ''}}Re: {{$json.subject}} - Cần bổ sung thông tin (Lần {{($json.informationRequestCount || 0) + 1}})", "message": "Chào bạn,\\n\\nTôi là {{$json.assignedTo}}, người phụ trách plan cho khách hàng {{$json.client}}.\\n\\n{{$json.shouldEscalate ? '⚠️ **ĐÂY LÀ LẦN THỨ ' + (($json.informationRequestCount || 0) + 1) + ' YÊU CẦU THÔNG TIN**\\n\\n' : ''}}Để tiến hành xây dựng kế hoạch chi tiết và hiệu quả nhất, tôi cần bạn hỗ trợ bổ sung các thông tin sau:\\n\\n🔴 **Thông tin bắt buộc (Critical):**\\n{{$json.missingCritical && $json.missingCritical.length > 0 ? $json.missingCritical.map(field => `- ${field}: [Vui lòng cung cấp]`).join('\\n') : 'Đã đầy đủ ✅'}}\\n\\n🟡 **Thông tin quan trọng (Important):**\\n{{$json.missingImportant && $json.missingImportant.length > 0 ? $json.missingImportant.map(field => `- ${field}: [Vui lòng cung cấp]`).join('\\n') : 'Đã đầy đủ ✅'}}\\n\\n⚪ **Thông tin tùy chọn (Optional):**\\n{{$json.missingOptional && $json.missingOptional.length > 0 ? $json.missingOptional.map(field => `- ${field}: [Nếu có]`).join('\\n') : 'Đã đầy đủ ✅'}}\\n\\n⏰ **Deadline:** {{$json.deadline}}\\n\\n{{$json.shouldEscalate ? '🚨 **Lưu ý:** Do đây là lần yêu cầu thứ ' + (($json.informationRequestCount || 0) + 1) + ', email này đã được CC cho Leader để theo dõi.\\n\\n' : ''}}Vui lòng phản hồi trong luồng email này trong vòng 24h. Cảm ơn bạn!\\n\\nTrân trọng,\\n{{$json.assignedTo}}"}, "position": [1000, 900]}, {"id": "20b", "name": "Update Information Request Count", "type": "n8n-nodes-base.function", "typeVersion": 2, "parameters": {"functionCode": "const data = $input.first().json;\nconst newCount = (data.informationRequestCount || 0) + 1;\n\nreturn [{ json: {\n  ...data,\n  informationRequestCount: newCount,\n  lastActivity: new Date().toISOString(),\n  escalated: newCount >= 2\n}}];"}, "position": [1250, 900]}, {"id": "21", "name": "Check Inventory & Budget", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "const data = $input.first().json;\n\nconst inventoryCheck = {\n  available: true,\n  formats: ['Banner', 'Video', 'Native'],\n  capacity: 1000000,\n  estimatedReach: 500000\n};\n\nconst budget = parseFloat(data.budget.replace(/[^0-9.]/g, '')) || 0;\nconst budgetAllocation = {\n  banner: Math.floor(budget * 0.4),\n  video: Math.floor(budget * 0.4),\n  native: Math.floor(budget * 0.2)\n};\n\nconst estimatedKPI = {\n  impressions: Math.floor(budget * 1000),\n  clicks: Math.floor(budget * 50),\n  conversions: Math.floor(budget * 5),\n  ctr: 5.0,\n  cpc: budget / 50\n};\n\nconst planDraft = {\n  orderId: data.orderId,\n  client: data.client,\n  budget: budget,\n  duration: data.duration,\n  formats: inventoryCheck.formats,\n  budgetAllocation: budgetAllocation,\n  estimatedKPI: estimatedKPI,\n  inventoryStatus: inventoryCheck.available ? 'Available' : 'Limited',\n  createdAt: new Date().toISOString(),\n  version: '1.0'\n};\n\nreturn [{ json: {\n  ...data,\n  status: 'Draft Created',\n  planDraft: JSON.stringify(planDraft)\n}}];"}, "position": [1000, 1000]}, {"id": "22", "name": "Notify Product Team", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "{{$env.PRODUCT_TEAM_EMAIL}}", "subject": "[Review Required] Plan Draft - {{$json.client}}", "message": "Chào team sản phẩm,\\n\\nCó một bản nháp plan cần review:\\n\\n- Order ID: {{$json.orderId}}\\n- Khách hàng: {{$json.client}}\\n- Ngân sách: {{$json.budget}}\\n- Thời gian: {{$json.duration}}\\n\\nBản nháp chi tiết:\\n{{$json.planDraft}}\\n\\nVui lòng review và phản hồi trong vòng 24h.\\n\\nTrân trọng,\\nHệ thống tự động"}, "position": [1000, 1100]}, {"id": "23", "name": "Finalize Plan", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "const data = $input.first().json;\n\nconst planDraft = JSON.parse(data.planDraft || '{}');\n\nconst finalPlan = {\n  ...planDraft,\n  productTeamApproved: true,\n  inventoryTeamApproved: true,\n  finalReviewDate: new Date().toISOString(),\n  version: '1.1',\n  status: 'Ready for Client'\n};\n\nreturn [{ json: {\n  ...data,\n  status: 'Ready for Client',\n  planFinal: JSON.stringify(finalPlan)\n}}];"}, "position": [1000, 1200]}, {"id": "24", "name": "Send Plan to Client", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "={{$json.sender}}", "subject": "<PERSON><PERSON> hoạch quảng cáo - {{$json.client}}", "message": "<PERSON><PERSON><PERSON> b<PERSON>,\\n\\nTôi xin gửi kế hoạch quảng cáo chi tiết cho {{$json.client}}:\\n\\n{{$json.planFinal}}\\n\\nVui lòng xem xét và phản hồi trong vòng 48h.\\n\\nTrân trọng,\\n{{$json.assignedTo}}"}, "position": [1000, 1300]}, {"id": "25", "name": "OpenAI - Analyze Client Feedback", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "parameters": {"operation": "chat", "model": "gpt-4o-mini", "messages": {"values": [{"role": "system", "content": "Bạn là AI chuyên phân tích phản hồi khách hàng về plan quảng cáo. Phân tích và trả về JSON với: feedback_type (approved/revision_needed/rejected/no_response/unclear), confidence_score (0-1), specific_requests (array), urgency_level (low/medium/high), sentiment (positive/neutral/negative), next_action (proceed/revise/escalate/close)."}, {"role": "user", "content": "<PERSON><PERSON><PERSON> h<PERSON> kh<PERSON>ch hàng: {{$json.clientFeedback}}"}]}, "options": {"temperature": 0.2, "maxTokens": 800}}, "position": [1000, 1400]}, {"id": "25b", "name": "Process Feedback Analysis", "type": "n8n-nodes-base.function", "typeVersion": 2, "parameters": {"functionCode": "try {\n  const data = $input.first().json;\n  const analysis = JSON.parse($input.first().json.choices[0].message.content);\n  \n  const currentRevisions = data.revisionCount || 0;\n  const maxRevisions = data.maxRevisions || 3;\n  \n  let newStatus, priority;\n  \n  switch (analysis.feedback_type) {\n    case 'approved':\n      newStatus = 'Approved';\n      priority = 'Normal';\n      break;\n      \n    case 'revision_needed':\n      if (currentRevisions >= maxRevisions) {\n        newStatus = 'Max Revisions Reached';\n        priority = 'High';\n      } else {\n        newStatus = 'Revision Required';\n        priority = analysis.urgency_level === 'high' ? 'High' : 'Medium';\n      }\n      break;\n      \n    case 'rejected':\n      newStatus = 'Closed - Rejected';\n      priority = 'Low';\n      break;\n      \n    case 'no_response':\n      newStatus = 'Awaiting Client Response';\n      priority = 'Medium';\n      break;\n      \n    default:\n      newStatus = 'Needs Clarification';\n      priority = 'Medium';\n  }\n  \n  return [{ json: {\n    ...data,\n    status: newStatus,\n    feedbackAnalysis: analysis,\n    revisionCount: analysis.feedback_type === 'revision_needed' ? currentRevisions + 1 : currentRevisions,\n    priority: priority,\n    lastActivity: new Date().toISOString(),\n    maxRevisionsReached: currentRevisions >= maxRevisions\n  }}];\n} catch (error) {\n  return [{ json: { error: 'Failed to process feedback analysis: ' + error.message } }];\n}"}, "position": [1250, 1400]}, {"id": "26", "name": "Final Storage", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "const data = $input.first().json;\n\nconst storagePath = `Plans/${data.client}/${data.orderId}_${new Date().toISOString().split('T')[0]}.json`;\n\nconst finalReport = {\n  orderId: data.orderId,\n  client: data.client,\n  product: data.product,\n  budget: data.budget,\n  duration: data.duration,\n  finalPlan: JSON.parse(data.planFinal || '{}'),\n  revisionCount: data.revisionCount || 0,\n  completedAt: new Date().toISOString(),\n  assignedTo: data.assignedTo,\n  status: 'Completed'\n};\n\nreturn [{ json: {\n  ...data,\n  status: 'Completed',\n  finalApproved: true,\n  storagePath: storagePath,\n  finalReport: JSON.stringify(finalReport)\n}}];"}, "position": [1000, 1500]}, {"id": "27", "name": "Update Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "parameters": {"operation": "update", "documentId": "{{$env.PLAN_SHEET_ID}}", "sheetName": "Plan Requests", "columnToMatchOn": "orderId", "valueToMatchOn": "={{$json.orderId}}", "columns": {"mappingMode": "defineBelow", "value": {"status": "={{$json.status}}", "assignedAt": "={{$json.assignedAt}}", "informationStatus": "={{$json.informationStatus}}", "planDraft": "={{$json.planDraft}}", "planFinal": "={{$json.planFinal}}", "clientFeedback": "={{$json.clientFeedback}}", "revisionCount": "={{$json.revisionCount}}", "finalApproved": "={{$json.finalApproved}}", "storagePath": "={{$json.storagePath}}", "assigneeConfirmed": "={{$json.assigneeConfirmed}}"}}}, "position": [1250, 1000]}, {"id": "28", "name": "Final Notification - Enhanced", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "parameters": {"operation": "send", "to": "{{$env.LEADER_EMAIL}},{{$json.sender}}", "subject": "[{{$json.status === 'Completed' ? '✅ COMPLETED' : '❌ CLOSED'}}] Order Plan - {{$json.client}}", "message": "Chào team,\\n\\nOrder plan đã được {{$json.status === 'Completed' ? 'hoàn thành' : 'đóng'}}:\\n\\n📋 **Chi tiết:**\\n- Order ID: {{$json.orderId}}\\n- Khách hàng: {{$json.client}}\\n- Trạng thái cuối: {{$json.status}}\\n- Người phụ trách: {{$json.assignedTo}}\\n- Số lần revision: {{$json.revisionCount || 0}}\\n- Thời gian xử lý: {{$json.receivedAt}} → {{$json.lastActivity}}\\n\\n{{$json.status === 'Completed' ? '📁 **Lưu trữ:** ' + $json.storagePath : '📝 **Lý do đóng:** ' + $json.status}}\\n\\n{{$json.feedbackAnalysis ? '💬 **Phân tích cuối:** ' + $json.feedbackAnalysis.sentiment + ' sentiment' : ''}}\\n\\nTrân trọng,\\nHệ thống tự động"}, "position": [1500, 1500]}, {"id": "29", "name": "Timeout Monitor - <PERSON><PERSON>", "type": "n8n-nodes-base.cron", "typeVersion": 1, "parameters": {"triggerTimes": {"item": [{"hour": 9, "minute": 0}, {"hour": 14, "minute": 0}, {"hour": 17, "minute": 0}]}}, "position": [250, 1800]}, {"id": "30", "name": "Check Overdue Orders", "type": "n8n-nodes-base.googleSheets", "typeVersion": 5, "parameters": {"operation": "read", "documentId": "{{$env.PLAN_SHEET_ID}}", "sheetName": "Plan Requests", "filters": {"conditions": [{"column": "status", "condition": "notEqual", "value": "Completed"}, {"column": "status", "condition": "notEqual", "value": "Closed - Rejected"}]}}, "position": [500, 1800]}, {"id": "31", "name": "Process Overdue Items", "type": "n8n-nodes-base.function", "typeVersion": 2, "parameters": {"functionCode": "const items = $input.all();\nconst now = new Date();\nconst overdueItems = [];\n\nfor (const item of items) {\n  const data = item.json;\n  const lastActivity = new Date(data.lastActivity || data.receivedAt);\n  const hoursSinceActivity = (now - lastActivity) / (1000 * 60 * 60);\n  \n  let isOverdue = false;\n  let urgencyLevel = 'normal';\n  \n  // <PERSON><PERSON>m tra c<PERSON>c đi<PERSON>u kiện overdue\n  if (data.status === 'New Order' && hoursSinceActivity > 2) {\n    isOverdue = true;\n    urgencyLevel = 'high';\n  } else if (data.status === 'Pending Information' && hoursSinceActivity > 48) {\n    isOverdue = true;\n    urgencyLevel = data.informationRequestCount >= 2 ? 'critical' : 'high';\n  } else if (data.status === 'Awaiting Client Response' && hoursSinceActivity > 72) {\n    isOverdue = true;\n    urgencyLevel = 'medium';\n  } else if (data.deadline) {\n    const deadline = new Date(data.deadline);\n    const hoursToDeadline = (deadline - now) / (1000 * 60 * 60);\n    if (hoursToDeadline < 24 && hoursToDeadline > 0) {\n      isOverdue = true;\n      urgencyLevel = 'critical';\n    }\n  }\n  \n  if (isOverdue) {\n    overdueItems.push({\n      ...data,\n      urgencyLevel: urgencyLevel,\n      hoursSinceActivity: Math.round(hoursSinceActivity)\n    });\n  }\n}\n\nreturn overdueItems.map(item => ({ json: item }));"}, "position": [750, 1800]}, {"id": "32", "name": "Send Overdue Alerts", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "parameters": {"operation": "send", "to": "{{$env.LEADER_EMAIL}}", "subject": "[🚨 OVERDUE ALERT] {{$json.urgencyLevel.toUpperCase()}} - Order {{$json.orderId}}", "message": "⚠️ **CẢNH BÁO OVERDUE**\\n\\n📋 **Chi tiết Order:**\\n- Order ID: {{$json.orderId}}\\n- Khách hàng: {{$json.client}}\\n- Trạng thái hiện tại: {{$json.status}}\\n- Người phụ trách: {{$json.assignedTo}}\\n- Mức độ khẩn cấp: {{$json.urgencyLevel.toUpperCase()}}\\n\\n⏰ **Thời gian:**\\n- Hoạt động cuối: {{$json.lastActivity}}\\n- Số giờ không hoạt động: {{$json.hoursSinceActivity}}h\\n- Deadline: {{$json.deadline}}\\n\\n🎯 **Hành động cần thiết:**\\n{{$json.urgencyLevel === 'critical' ? '- LIÊN HỆ NGAY với assignee\\n- Xem xét reassign nếu cần' : $json.urgencyLevel === 'high' ? '- Follow up với assignee\\n- Kiểm tra tiến độ' : '- Nhắc nhở assignee\\n- Cập nhật timeline'}}\\n\\nTrân trọng,\\nHệ thống giám sát"}, "position": [1000, 1800]}], "connections": {"Gmail Trigger - New Order": {"main": [[{"node": "OpenAI - Extract Order Information", "type": "main", "index": 0}]]}, "OpenAI - Extract Order Information": {"main": [[{"node": "Process Extracted Data", "type": "main", "index": 0}]]}, "Process Extracted Data": {"main": [[{"node": "Save to Google Sheets", "type": "main", "index": 0}]]}, "Save to Google Sheets": {"main": [[{"node": "Notify Leader Initial", "type": "main", "index": 0}]]}, "Notify Leader Initial": {"main": [[{"node": "Wait 30m for Leader", "type": "main", "index": 0}]]}, "Wait 30m for Leader": {"main": [[{"node": "Check If Assigned", "type": "main", "index": 0}]]}, "Check If Assigned": {"main": [[{"node": "Is Leader Assigned?", "type": "main", "index": 0}]]}, "Is Leader Assigned?": {"main": [[{"node": "Remind Leader", "type": "main", "index": 0}], []]}, "Remind Leader": {"main": [[{"node": "Wait 15m <PERSON>minder", "type": "main", "index": 0}]]}, "Wait 15m Reminder": {"main": [[{"node": "Check If Assigned", "type": "main", "index": 0}]]}, "Google Sheets Trigger - Status Update": {"main": [[{"node": "Check Status and Route", "type": "main", "index": 0}]]}, "Check Status and Route": {"main": [[{"node": "Route Switch", "type": "main", "index": 0}]]}, "Route Switch": {"main": [[], [{"node": "Ask Assignee Confirm", "type": "main", "index": 0}], [{"node": "Check Information Complete - Enhanced", "type": "main", "index": 0}], [{"node": "Request Missing Information - Enhanced", "type": "main", "index": 0}], [{"node": "Check Inventory & Budget", "type": "main", "index": 0}], [{"node": "Notify Product Team", "type": "main", "index": 0}], [{"node": "Finalize Plan", "type": "main", "index": 0}], [{"node": "Send Plan to Client", "type": "main", "index": 0}], [{"node": "OpenAI - Analyze Client Feedback", "type": "main", "index": 0}], [{"node": "Final Storage", "type": "main", "index": 0}]]}, "Ask Assignee Confirm": {"main": [[{"node": "Wait 30m for <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Wait 30m for Assignee": {"main": [[{"node": "Check Assignee Confirmed", "type": "main", "index": 0}]]}, "Check Assignee Confirmed": {"main": [[{"node": "Is As<PERSON>ee Confirmed?", "type": "main", "index": 0}]]}, "Is Assignee Confirmed?": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}], []]}, "Remind Assignee": {"main": [[{"node": "Wait 15m <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Wait 15m Assignee Reminder": {"main": [[{"node": "Check Assignee Confirmed", "type": "main", "index": 0}]]}, "Check Information Complete - Enhanced": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Request Missing Information - Enhanced": {"main": [[{"node": "Update Information Request Count", "type": "main", "index": 0}]]}, "Update Information Request Count": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Check Inventory & Budget": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Notify Product Team": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Finalize Plan": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Send Plan to Client": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "OpenAI - Analyze Client Feedback": {"main": [[{"node": "Process Feedback Analysis", "type": "main", "index": 0}]]}, "Process Feedback Analysis": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Final Storage": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Update Google Sheets": {"main": [[{"node": "Final Notification - Enhanced", "type": "main", "index": 0}]]}, "Timeout Monitor - Cron Trigger": {"main": [[{"node": "Check Overdue Orders", "type": "main", "index": 0}]]}, "Check Overdue Orders": {"main": [[{"node": "Process Overdue Items", "type": "main", "index": 0}]]}, "Process Overdue Items": {"main": [[{"node": "Send Overdue Alerts", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner"}, "staticData": null, "tags": ["order-plan", "automation", "ai-enhanced", "monitoring"], "triggerCount": 3, "updatedAt": "2024-12-15T12:00:00.000Z", "versionId": "2.0"}