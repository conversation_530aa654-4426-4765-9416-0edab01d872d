{"name": "Order Plan Processing Workflow - Complete Fixed", "version": 1, "nodes": [{"id": "1", "name": "<PERSON><PERSON> - New Order", "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1, "parameters": {"triggerOn": "newEmail", "filters": {"label": "Order-Plan"}}, "position": [250, 300], "webhookId": "gmail-trigger"}, {"id": "2", "name": "Extract Order Information", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "const email = $input.first().json;\nconst subject = email.subject || '';\nconst body = email.textPlain || email.textHtml || '';\n\nconst patterns = {\n  client: /(?:client|khách hàng|brand)[:：]\\s*([^\\n\\r]+)/i,\n  product: /(?:sản phẩm|product|link web)[:：]\\s*([^\\n\\r]+)/i,\n  budget: /(?:ngân sách|budget|chi phí)[:：]\\s*([^\\n\\r]+)/i,\n  duration: /(?:thời gian|duration|thời hạn)[:：]\\s*([^\\n\\r]+)/i,\n  kpi: /(?:kpi|mục tiêu|target)[:：]\\s*([^\\n\\r]+)/i,\n  deadline: /(?:deadline|hạn chót)[:：]\\s*([^\\n\\r]+)/i,\n  industry: /(?:ngành|industry|lĩnh vực)[:：]\\s*([^\\n\\r]+)/i,\n  targetAudience: /(?:target audience|đối tượng)[:：]\\s*([^\\n\\r]+)/i\n};\n\nconst extractedData = {};\nfor (const [key, pattern] of Object.entries(patterns)) {\n  const match = body.match(pattern);\n  extractedData[key] = match ? match[1].trim() : '';\n}\n\nconst orderId = 'ORD-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);\n\nconst orderData = {\n  orderId: orderId,\n  emailId: email.id,\n  threadId: email.threadId,\n  sender: email.from,\n  subject: subject,\n  receivedAt: new Date().toISOString(),\n  status: 'New Order',\n  client: extractedData.client,\n  product: extractedData.product,\n  budget: extractedData.budget,\n  duration: extractedData.duration,\n  kpi: extractedData.kpi,\n  deadline: extractedData.deadline,\n  industry: extractedData.industry,\n  targetAudience: extractedData.targetAudience,\n  rawEmail: body,\n  assignedTo: '',\n  assignedAt: '',\n  informationStatus: 'Pending Review',\n  planDraft: '',\n  planFinal: '',\n  clientFeedback: '',\n  revisionCount: 0,\n  finalApproved: false,\n  storagePath: '',\n  leaderNotified: true,\n  assigneeConfirmed: false\n};\n\nreturn [{ json: orderData }];"}, "position": [500, 300]}, {"id": "3", "name": "Save to Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "parameters": {"operation": "append", "documentId": "{{$env.PLAN_SHEET_ID}}", "sheetName": "Plan Requests", "columns": {"mappingMode": "defineBelow", "value": {"orderId": "={{$json.orderId}}", "emailId": "={{$json.emailId}}", "threadId": "={{$json.threadId}}", "sender": "={{$json.sender}}", "subject": "={{$json.subject}}", "receivedAt": "={{$json.receivedAt}}", "status": "={{$json.status}}", "client": "={{$json.client}}", "product": "={{$json.product}}", "budget": "={{$json.budget}}", "duration": "={{$json.duration}}", "kpi": "={{$json.kpi}}", "deadline": "={{$json.deadline}}", "industry": "={{$json.industry}}", "targetAudience": "={{$json.targetAudience}}", "assignedTo": "={{$json.assignedTo}}", "assignedAt": "={{$json.assignedAt}}", "informationStatus": "={{$json.informationStatus}}", "planDraft": "={{$json.planDraft}}", "planFinal": "={{$json.planFinal}}", "clientFeedback": "={{$json.clientFeedback}}", "revisionCount": "={{$json.revisionCount}}", "finalApproved": "={{$json.finalApproved}}", "storagePath": "={{$json.storagePath}}"}}}, "position": [750, 300]}, {"id": "4", "name": "Notify Leader Initial", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "{{$env.LEADER_EMAIL}}", "subject": "[URGENT] New Order Plan - {{$json.client}}", "message": "Ch<PERSON>o Leader,\\n\\nCó một order plan mới cần đ<PERSON> x<PERSON> lý:\\n\\n- Order ID: {{$json.orderId}}\\n- Khách hàng: {{$json.client}}\\n- Sản phẩm: {{$json.product}}\\n- Ngân sách: {{$json.budget}}\\n- Deadline: {{$json.deadline}}\\n- Thời gian nhận: {{$json.receivedAt}}\\n\\nVui lòng truy cập Google Sheets để assign người phụ trách trong vòng 30 phút.\\n\\nTrân trọng,\\nHệ thống tự động"}, "position": [1000, 300]}, {"id": "5", "name": "Wait 30m for Leader", "type": "n8n-nodes-base.wait", "typeVersion": 1, "parameters": {"amount": 30, "unit": "minutes"}, "position": [1250, 300]}, {"id": "6", "name": "Check If Assigned", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "parameters": {"operation": "read", "documentId": "{{$env.PLAN_SHEET_ID}}", "sheetName": "Plan Requests", "filters": {"conditions": [{"column": "orderId", "condition": "equal", "value": "={{$json.orderId}}"}]}}, "position": [1500, 300]}, {"id": "7", "name": "Is Leader Assigned?", "type": "n8n-nodes-base.if", "typeVersion": 2, "parameters": {"conditions": {"string": [{"value1": "={{$json.assignedTo}}", "operation": "isEmpty"}]}}, "position": [1750, 300]}, {"id": "8", "name": "Remind Leader", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "{{$env.LEADER_EMAIL}}", "subject": "[REMINDER] Chưa assign người phụ trách cho {{$json.orderId}}", "message": "<PERSON><PERSON> assign người phụ trách cho order {{$json.orderId}} ({{$json.client}}). <PERSON><PERSON> thống sẽ tiếp tục nhắc mỗi 15 phút cho tới khi hoàn tất."}, "position": [2000, 200]}, {"id": "9", "name": "Wait 15m <PERSON>minder", "type": "n8n-nodes-base.wait", "typeVersion": 1, "parameters": {"amount": 15, "unit": "minutes"}, "position": [2250, 200]}, {"id": "10", "name": "Google Sheets Trigger - Status Update", "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "parameters": {"documentId": "{{$env.PLAN_SHEET_ID}}", "sheetName": "Plan Requests", "triggerOn": "update"}, "position": [250, 800], "webhookId": "sheets-trigger"}, {"id": "11", "name": "Check Status and Route", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "const data = $input.first().json;\nconst status = data.status || '';\nconst assignedTo = data.assignedTo || '';\nconst assigneeConfirmed = data.assigneeConfirmed || false;\nconst informationStatus = data.informationStatus || '';\n\nlet nextStep = 'unknown';\n\n// <PERSON><PERSON><PERSON> tra nếu vừa được assign\nif (status === 'New Order' && assignedTo && !assigneeConfirmed) {\n  nextStep = 'ask_assignee_confirm';\n}\n// Nếu assignee đã confirm\nelse if (assigneeConfirmed && informationStatus === 'Pending Review') {\n  nextStep = 'check_information';\n}\n// <PERSON>ác trạng thái khác\nelse if (informationStatus === 'Insufficient') {\n  nextStep = 'request_info';\n}\nelse if (status === 'Information Sufficient') {\n  nextStep = 'check_inventory';\n}\nelse if (status === 'Draft Created') {\n  nextStep = 'notify_product_team';\n}\nelse if (status === 'Internal Review Complete') {\n  nextStep = 'finalize_plan';\n}\nelse if (status === 'Ready for Client') {\n  nextStep = 'send_to_client';\n}\nelse if (status === 'Client Feedback') {\n  nextStep = 'process_feedback';\n}\nelse if (status === 'Approved') {\n  nextStep = 'final_storage';\n}\n\nreturn [{ json: { ...data, nextStep } }];"}, "position": [500, 800]}, {"id": "12", "name": "Route Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "parameters": {"options": {"fallbackOutput": 0}, "rules": {"values": [{"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "ask_assignee_confirm"}}, "renameOutput": true, "outputKey": "ask_confirm"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "check_information"}}, "renameOutput": true, "outputKey": "check_info"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "request_info"}}, "renameOutput": true, "outputKey": "request_info"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "check_inventory"}}, "renameOutput": true, "outputKey": "inventory"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "notify_product_team"}}, "renameOutput": true, "outputKey": "product"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "finalize_plan"}}, "renameOutput": true, "outputKey": "finalize"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "send_to_client"}}, "renameOutput": true, "outputKey": "send_client"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "process_feedback"}}, "renameOutput": true, "outputKey": "feedback"}, {"conditions": {"options": {"leftValue": "={{$json.nextStep}}", "operation": "equal", "rightValue": "final_storage"}}, "renameOutput": true, "outputKey": "storage"}]}}, "position": [750, 800]}, {"id": "13", "name": "Ask Assignee Confirm", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "={{$json.assignedTo}}", "subject": "[CONFIRM] Nhận xử lý order {{$json.orderId}} - {{$json.client}}", "message": "<PERSON><PERSON><PERSON> b<PERSON>,\\n\\nB<PERSON><PERSON> assign xử lý order plan sau:\\n\\n- Order ID: {{$json.orderId}}\\n- Khách hàng: {{$json.client}}\\n- Sản phẩm: {{$json.product}}\\n- Ngân sách: {{$json.budget}}\\n- Deadline: {{$json.deadline}}\\n\\nVui lòng reply email này hoặc cập nhật cột 'assigneeConfirmed' = TRUE trong Google Sheet để xác nhận tiếp nhận.\\n\\nTrân trọng,\\nHệ thống tự động"}, "position": [1000, 600]}, {"id": "14", "name": "Wait 30m for <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.wait", "typeVersion": 1, "parameters": {"amount": 30, "unit": "minutes"}, "position": [1250, 600]}, {"id": "15", "name": "Check Assignee Confirmed", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "parameters": {"operation": "read", "documentId": "{{$env.PLAN_SHEET_ID}}", "sheetName": "Plan Requests", "filters": {"conditions": [{"column": "orderId", "condition": "equal", "value": "={{$json.orderId}}"}]}}, "position": [1500, 600]}, {"id": "16", "name": "Is As<PERSON>ee Confirmed?", "type": "n8n-nodes-base.if", "typeVersion": 2, "parameters": {"conditions": {"boolean": [{"value1": "={{$json.assigneeConfirmed}}", "operation": "equal", "value2": false}]}}, "position": [1750, 600]}, {"id": "17", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "={{$json.assignedTo}}", "subject": "[REMINDER] <PERSON><PERSON><PERSON> nhận tiếp nhận order {{$json.orderId}}", "message": "Bạn chưa xác nhận tiếp nhận order {{$json.orderId}} ({{$json.client}}). <PERSON><PERSON> lòng xác nhận sớm để có thể tiến hành xử lý."}, "position": [2000, 500]}, {"id": "18", "name": "Wait 15m <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.wait", "typeVersion": 1, "parameters": {"amount": 15, "unit": "minutes"}, "position": [2250, 500]}, {"id": "19", "name": "Check Information Complete", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "const data = $input.first().json;\n\nconst requiredFields = [\n  'client', 'product', 'budget', 'duration', \n  'kpi', 'deadline', 'industry', 'targetAudience'\n];\n\nconst missingFields = [];\nfor (const field of requiredFields) {\n  if (!data[field] || data[field].trim() === '') {\n    missingFields.push(field);\n  }\n}\n\nlet newStatus, newInformationStatus;\nif (missingFields.length === 0) {\n  newStatus = 'Information Sufficient';\n  newInformationStatus = 'Complete';\n} else {\n  newStatus = 'Pending Information';\n  newInformationStatus = 'Insufficient';\n}\n\nreturn [{ json: {\n  ...data,\n  status: newStatus,\n  informationStatus: newInformationStatus,\n  missingFields: missingFields\n}}];"}, "position": [1000, 700]}, {"id": "20", "name": "Request Missing Information", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "={{$json.sender}}", "subject": "Re: {{$json.subject}}", "message": "<PERSON><PERSON><PERSON> bạn,\\n\\nTôi là {{$json.assignedTo}}, người phụ trách plan cho khách hàng {{$json.client}}.\\n\\nĐể tiến hành xây dựng kế hoạch chi tiết và hiệu quả nhất, tôi cần bạn hỗ trợ bổ sung các thông tin sau:\\n\\n{{$json.missingFields ? $json.missingFields.map(field => `- ${field}: [Thông tin cần bổ sung]`).join('\\n') : ''}}\\n\\nVui lòng phản hồi trong luồng email này. Cảm ơn bạn!\\n\\nTrân trọng,\\n{{$json.assignedTo}}"}, "position": [1000, 900]}, {"id": "21", "name": "Check Inventory & Budget", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "const data = $input.first().json;\n\nconst inventoryCheck = {\n  available: true,\n  formats: ['Banner', 'Video', 'Native'],\n  capacity: 1000000,\n  estimatedReach: 500000\n};\n\nconst budget = parseFloat(data.budget.replace(/[^0-9.]/g, '')) || 0;\nconst budgetAllocation = {\n  banner: Math.floor(budget * 0.4),\n  video: Math.floor(budget * 0.4),\n  native: Math.floor(budget * 0.2)\n};\n\nconst estimatedKPI = {\n  impressions: Math.floor(budget * 1000),\n  clicks: Math.floor(budget * 50),\n  conversions: Math.floor(budget * 5),\n  ctr: 5.0,\n  cpc: budget / 50\n};\n\nconst planDraft = {\n  orderId: data.orderId,\n  client: data.client,\n  budget: budget,\n  duration: data.duration,\n  formats: inventoryCheck.formats,\n  budgetAllocation: budgetAllocation,\n  estimatedKPI: estimatedKPI,\n  inventoryStatus: inventoryCheck.available ? 'Available' : 'Limited',\n  createdAt: new Date().toISOString(),\n  version: '1.0'\n};\n\nreturn [{ json: {\n  ...data,\n  status: 'Draft Created',\n  planDraft: JSON.stringify(planDraft)\n}}];"}, "position": [1000, 1000]}, {"id": "22", "name": "Notify Product Team", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "{{$env.PRODUCT_TEAM_EMAIL}}", "subject": "[Review Required] Plan Draft - {{$json.client}}", "message": "Chào team sản phẩm,\\n\\nCó một bản nháp plan cần review:\\n\\n- Order ID: {{$json.orderId}}\\n- Khách hàng: {{$json.client}}\\n- Ngân sách: {{$json.budget}}\\n- Thời gian: {{$json.duration}}\\n\\nBản nháp chi tiết:\\n{{$json.planDraft}}\\n\\nVui lòng review và phản hồi trong vòng 24h.\\n\\nTrân trọng,\\nHệ thống tự động"}, "position": [1000, 1100]}, {"id": "23", "name": "Finalize Plan", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "const data = $input.first().json;\n\nconst planDraft = JSON.parse(data.planDraft || '{}');\n\nconst finalPlan = {\n  ...planDraft,\n  productTeamApproved: true,\n  inventoryTeamApproved: true,\n  finalReviewDate: new Date().toISOString(),\n  version: '1.1',\n  status: 'Ready for Client'\n};\n\nreturn [{ json: {\n  ...data,\n  status: 'Ready for Client',\n  planFinal: JSON.stringify(finalPlan)\n}}];"}, "position": [1000, 1200]}, {"id": "24", "name": "Send Plan to Client", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "={{$json.sender}}", "subject": "<PERSON><PERSON> hoạch quảng cáo - {{$json.client}}", "message": "<PERSON><PERSON><PERSON> b<PERSON>,\\n\\nTôi xin gửi kế hoạch quảng cáo chi tiết cho {{$json.client}}:\\n\\n{{$json.planFinal}}\\n\\nVui lòng xem xét và phản hồi trong vòng 48h.\\n\\nTrân trọng,\\n{{$json.assignedTo}}"}, "position": [1000, 1300]}, {"id": "25", "name": "Process Client Feedback", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "const data = $input.first().json;\nconst feedback = data.clientFeedback || '';\n\nlet feedbackType = 'unknown';\nif (feedback.toLowerCase().includes('đồng ý') || feedback.toLowerCase().includes('ok') || feedback.toLowerCase().includes('chốt')) {\n  feedbackType = 'approved';\n} else if (feedback.toLowerCase().includes('sửa') || feedback.toLowerCase().includes('thay đổi') || feedback.toLowerCase().includes('revise')) {\n  feedbackType = 'revision';\n} else if (feedback.toLowerCase().includes('từ chối') || feedback.toLowerCase().includes('không')) {\n  feedbackType = 'rejected';\n}\n\nlet newStatus, revisionCount;\nif (feedbackType === 'approved') {\n  newStatus = 'Approved';\n  revisionCount = data.revisionCount || 0;\n} else if (feedbackType === 'revision') {\n  newStatus = 'Information Sufficient';\n  revisionCount = (data.revisionCount || 0) + 1;\n} else if (feedbackType === 'rejected') {\n  newStatus = 'Closed - Not Won';\n  revisionCount = data.revisionCount || 0;\n}\n\nreturn [{ json: {\n  ...data,\n  status: newStatus,\n  revisionCount: revisionCount,\n  feedbackType: feedbackType\n}}];"}, "position": [1000, 1400]}, {"id": "26", "name": "Final Storage", "type": "n8n-nodes-base.function", "typeVersion": 1, "parameters": {"functionCode": "const data = $input.first().json;\n\nconst storagePath = `Plans/${data.client}/${data.orderId}_${new Date().toISOString().split('T')[0]}.json`;\n\nconst finalReport = {\n  orderId: data.orderId,\n  client: data.client,\n  product: data.product,\n  budget: data.budget,\n  duration: data.duration,\n  finalPlan: JSON.parse(data.planFinal || '{}'),\n  revisionCount: data.revisionCount || 0,\n  completedAt: new Date().toISOString(),\n  assignedTo: data.assignedTo,\n  status: 'Completed'\n};\n\nreturn [{ json: {\n  ...data,\n  status: 'Completed',\n  finalApproved: true,\n  storagePath: storagePath,\n  finalReport: JSON.stringify(finalReport)\n}}];"}, "position": [1000, 1500]}, {"id": "27", "name": "Update Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "parameters": {"operation": "update", "documentId": "{{$env.PLAN_SHEET_ID}}", "sheetName": "Plan Requests", "columnToMatchOn": "orderId", "valueToMatchOn": "={{$json.orderId}}", "columns": {"mappingMode": "defineBelow", "value": {"status": "={{$json.status}}", "assignedAt": "={{$json.assignedAt}}", "informationStatus": "={{$json.informationStatus}}", "planDraft": "={{$json.planDraft}}", "planFinal": "={{$json.planFinal}}", "clientFeedback": "={{$json.clientFeedback}}", "revisionCount": "={{$json.revisionCount}}", "finalApproved": "={{$json.finalApproved}}", "storagePath": "={{$json.storagePath}}", "assigneeConfirmed": "={{$json.assigneeConfirmed}}"}}}, "position": [1250, 1000]}, {"id": "28", "name": "Final Notification", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "parameters": {"operation": "send", "to": "{{$env.LEADER_EMAIL}},{{$json.sender}}", "subject": "[Completed] Order Plan - {{$json.client}}", "message": "Chào team,\\n\\nOrder plan đã được hoàn thành:\\n\\n- Order ID: {{$json.orderId}}\\n- Khách hàng: {{$json.client}}\\n- Trạng thái: {{$json.status}}\\n- Người phụ trách: {{$json.assignedTo}}\\n- Đường dẫn lưu trữ: {{$json.storagePath}}\\n\\nTrân trọng,\\nHệ thống tự động"}, "position": [1500, 1500]}], "connections": {"Gmail Trigger - New Order": {"main": [[{"node": "Extract Order Information", "type": "main", "index": 0}]]}, "Extract Order Information": {"main": [[{"node": "Save to Google Sheets", "type": "main", "index": 0}]]}, "Save to Google Sheets": {"main": [[{"node": "Notify Leader Initial", "type": "main", "index": 0}]]}, "Notify Leader Initial": {"main": [[{"node": "Wait 30m for Leader", "type": "main", "index": 0}]]}, "Wait 30m for Leader": {"main": [[{"node": "Check If Assigned", "type": "main", "index": 0}]]}, "Check If Assigned": {"main": [[{"node": "Is Leader Assigned?", "type": "main", "index": 0}]]}, "Is Leader Assigned?": {"main": [[{"node": "Remind Leader", "type": "main", "index": 0}], []]}, "Remind Leader": {"main": [[{"node": "Wait 15m <PERSON>minder", "type": "main", "index": 0}]]}, "Wait 15m Reminder": {"main": [[{"node": "Check If Assigned", "type": "main", "index": 0}]]}, "Google Sheets Trigger - Status Update": {"main": [[{"node": "Check Status and Route", "type": "main", "index": 0}]]}, "Check Status and Route": {"main": [[{"node": "Route Switch", "type": "main", "index": 0}]]}, "Route Switch": {"main": [[], [{"node": "Ask Assignee Confirm", "type": "main", "index": 0}], [{"node": "Check Information Complete", "type": "main", "index": 0}], [{"node": "Request Missing Information", "type": "main", "index": 0}], [{"node": "Check Inventory & Budget", "type": "main", "index": 0}], [{"node": "Notify Product Team", "type": "main", "index": 0}], [{"node": "Finalize Plan", "type": "main", "index": 0}], [{"node": "Send Plan to Client", "type": "main", "index": 0}], [{"node": "Process Client Feedback", "type": "main", "index": 0}], [{"node": "Final Storage", "type": "main", "index": 0}]]}, "Ask Assignee Confirm": {"main": [[{"node": "Wait 30m for <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Wait 30m for Assignee": {"main": [[{"node": "Check Assignee Confirmed", "type": "main", "index": 0}]]}, "Check Assignee Confirmed": {"main": [[{"node": "Is As<PERSON>ee Confirmed?", "type": "main", "index": 0}]]}, "Is Assignee Confirmed?": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}], []]}, "Remind Assignee": {"main": [[{"node": "Wait 15m <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Wait 15m Assignee Reminder": {"main": [[{"node": "Check Assignee Confirmed", "type": "main", "index": 0}]]}, "Check Information Complete": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Request Missing Information": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Check Inventory & Budget": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Notify Product Team": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Finalize Plan": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Send Plan to Client": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Process Client Feedback": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Final Storage": {"main": [[{"node": "Update Google Sheets", "type": "main", "index": 0}]]}, "Update Google Sheets": {"main": [[{"node": "Final Notification", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": ["order-plan", "automation"], "triggerCount": 2, "updatedAt": "2024-12-15T00:00:00.000Z", "versionId": "2"}